import { ForbiddenException, NotFoundException } from "@nestjs/common";
import { CurrentUser } from "src/auth/types";
import { getError } from "./errors";
import { UserRole } from "@prisma/client";

export abstract class BaseService {
    constructor(readonly entityType: string) {}

    abstract canGet(id: string, user: CurrentUser): Promise<true>;

    async canChange(id: string, user: CurrentUser): Promise<true> {
        if (user.role === UserRole.admin) return true;

        throw new ForbiddenException(...getError("must_be_admin"));
    }

    protected createNotFoundException() {
        return new NotFoundException(
            ...getError(`${this.entityType}_not_found`),
        );
    }

    protected async _check<T = unknown>(
        ids: string[],
        repo: {
            findMany: (params: {
                where: {
                    id: {
                        in: string[];
                    };
                    deletedAt?: null;
                };
            }) => Promise<T[]>;
        },
        notFoundDescription: string,
    ): Promise<T[]> {
        ids = [...new Set(ids)];

        const items = await repo.findMany({
            where: {
                id: { in: ids },
                deletedAt: null,
            },
        });

        if (items.length < ids.length) {
            throw new NotFoundException(notFoundDescription);
        }

        return items;
    }
}
