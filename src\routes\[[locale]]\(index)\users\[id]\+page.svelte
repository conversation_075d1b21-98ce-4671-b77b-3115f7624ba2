<script lang="ts">
  import { findLocalizationForUserLocales } from "$lib";
  import DetailImageCarousel from "./detail-image-carousel.svelte";

  const i18n = {
    en: {
      _page: {
        title: "— Commune",
      },
      userNotFound: "User not found",
      noDescription: "No description available",
      userDetails: "User Details",
      joinedOn: "Joined on",
      dateFormatLocale: "en-US",
    },
    ru: {
      _page: {
        title: "— Коммуна",
      },
      userNotFound: "Пользователь не найден",
      noDescription: "Нет описания",
      userDetails: "Информация о пользователе",
      joinedOn: "Дата регистрации",
      dateFormatLocale: "ru-RU",
    },
  };

  const { data } = $props();
  const { locale, routeLocale } = $derived(data);

  const t = $derived(i18n[locale]);

  // Get user data from SSR
  const user = $derived(data.user);

  // Derived values
  const userName = $derived(user ? findLocalizationForUserLocales(user.name, routeLocale) : "");
  const userDescription = $derived(
    user ? findLocalizationForUserLocales(user.description, routeLocale) : "",
  );

  const joinDate = $derived(user ? new Date(user.createdAt) : new Date());
  const formattedDate = $derived(
    joinDate.toLocaleDateString(t.dateFormatLocale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  );

  // Get badge class based on user role
  const getBadgeClass = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-danger";
      case "moderator":
        return "bg-warning";
      default:
        return "bg-primary";
    }
  };
</script>

<svelte:head>
  <title>{userName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  {#if !user}
    <div class="alert alert-danger" role="alert">
      {t.userNotFound}
    </div>
  {:else}
    <div class="row">
      <div class="col-lg-8">
        <!-- Image Carousel -->
        <DetailImageCarousel images={user.images || []} {locale} />

        <!-- User Information -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">{userName}</h2>
            <span class={`badge ${getBadgeClass(user.role)}`}>
              {user.role}
            </span>
          </div>
          <p class="lead text-muted">{userDescription || t.noDescription}</p>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <h5 class="card-title">{t.userDetails}</h5>
            <hr />
            <div class="d-flex align-items-center mb-3">
              <i class="bi bi-envelope-fill me-2 text-primary"></i>
              <span>{user.email}</span>
            </div>
            <div class="d-flex align-items-center">
              <i class="bi bi-calendar-date me-2 text-primary"></i>
              <span>{t.joinedOn} {formattedDate}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
