<script lang="ts">
  import type { DictionaryKey } from "./dictionaries.js";

  import { lightDictionaryColors } from "./dictionary-colors";
  import ExamplesSection from "./examples-section.svelte";
  import { rules } from "./rules";

  const alphabet = [
    "Aa",
    "<PERSON>ā",
    "Bb",
    "Čč",
    "Dd",
    "Ee",
    "Ëë",
    "Ff",
    "Gg",
    "Hh",
    "Ii",
    "Íí",
    "Jj",
    "Kk",
    "Ll",
    "Mm",
    "Nn",
    "Oo",
    "Ōō",
    "Pp",
    "Rr",
    "Ss",
    "Šš",
    "Tt",
    "Uu",
    "Vv",
    "Ww",
    "Yy",
    "Zz",
  ];

  const i18n = {
    en: {
      _page: {
        title: "New English — Commune",
      },
      title: "New English: A Phonetic Revolution",
      problem: {
        title: "The Challenge",
        description:
          "English writing and pronunciation have diverged significantly over centuries, creating numerous challenges.",
        difficulties: {
          description: "The current English language presents several difficulties",
          list: [
            "Significant difference between written and spoken forms",
            "Inconsistent pronunciation rules with many exceptions",
            "Difficult accents that vary across regions",
            "Overcomplicated spelling rules that don't match pronunciation",
            "Natural reduction in spoken language not reflected in writing",
            "Redundant elements like multiple ways to represent the same sound",
          ],
        },
      },
      idea: {
        title: "Early Attempts",
        description: {
          1: "New English: Learning from Early Challenges in Phonetic Reform",
          2: 'Our journey began with a simple question: "How might English evolve over time?" Our first attempt at creating New English faced significant challenges, as it incorporated overly aggressive simplifications that ultimately compromised the system\'s integrity.',
          3: "This early version relied on online translator pronunciations rather than established dictionaries. This approach proved problematic, as it lacked the linguistic foundation necessary to establish consistent rules. The resulting translations were unstable, inconsistent across platforms, and resistant to systematic automation.",
        },
      },
      implementation: {
        title: "Our Implementation",
        description: "A systematic approach to reforming English spelling and pronunciation.",
        features: {
          description: "Our New English implementation features",
          list: [
            "Words written exactly as they are pronounced",
            "Reduction of indefinite articles to their essential forms",
            'Simplification of definite articles and demonstratives to "da"',
            "Consistent phonetic representation for all sounds",
            "Elimination of silent letters and redundant spellings",
            "Preservation of word recognition while improving logical consistency",
          ],
        },
      },
      dictionaries: {
        title: "Reference Dictionaries",
        description:
          "Our system is based on established phonetic standards from these authoritative sources",
        cambridge: {
          name: "Cambridge English Dictionary",
          description: "A comprehensive dictionary of English with phonetic transcriptions",
        },
        opendict: {
          name: "Open Dictionary",
          description: "Open-licensed dictionary data",
        },
      },
      sources: {
        title: "Research Sources",
        description: "Our approach is informed by these linguistic resources",
        englishPhonology: {
          name: "English Phonology - Wikipedia",
          description: "Overview of the sound system of the English language",
        },
        internationalPhoneticAlphabet: {
          name: "International Phonetic Alphabet",
          description: "Standardized representation of sounds in written form",
        },
        americanIpaChart: {
          name: "American IPA Chart",
          description: "Chart of the American IPA",
        },
      },
      dialects: {
        title: "Reference Dialects",
        description: "Our phonetic system prioritizes these dialects in descending order",
        generalAmerican: {
          name: "General American",
          description: "The accent of American English most commonly perceived as neutral",
        },
        standardEnglish: {
          name: "Standard English",
          description: "The standard accent of Standard English in the United Kingdom",
        },
        localDialect: {
          name: "Local Dialect",
          description: "The form of English used in the local area",
        },
      },
      alphabet: {
        title: "New English Alphabet",
      },
      rules: {
        title: "Phonetic Rules",
        description: "Each sound is consistently represented by specific letter(s)",
        phoneticSounds: "Phonetic Sounds",
        newEnglishLetter: "New English Letter(s)",
        exampleWords: "Example Words",
      },
      nuances: {
        title: "Language Nuances",
        description:
          "New English includes several practical modifications to improve consistency and pronunciation flow",
        pronoun: {
          title: "Personal Pronouns",
          description:
            "New English uses 'Me' instead of 'I' for consistency with other pronoun forms and to eliminate the arbitrary capitalization rule.",
        },
        interrogatives: {
          title: "Question Words",
          description:
            "Question words often use the '-sa' suffix to avoid consonant clusters and improve pronunciation flow:",
          tableHeaders: {
            original: "Original",
            preferred: "Preferred",
            expected: "Expected",
          },
          examples: [
            {
              before: "what",
              after: {
                preferred: "wotsa",
                expected: "wot",
              },
            },
            {
              before: "who",
              after: {
                preferred: "hysa",
                expected: "hy",
              },
            },
            {
              before: "when",
              after: {
                preferred: "wensa",
                expected: "wen",
              },
            },
            {
              before: "where",
              after: {
                preferred: "wersa",
                expected: "wer",
              },
            },
            {
              before: "which",
              after: {
                preferred: "wičsa",
                expected: "wič",
              },
            },
            {
              before: "why",
              after: {
                preferred: "waísa",
                expected: "waí",
              },
            },
            {
              before: "how",
              after: {
                preferred: "haysa",
                expected: "hay",
              },
            },

            {
              before: "there",
              after: {
                preferred: "tersa",
                expected: "ter",
              },
            },
            {
              before: "then",
              after: {
                preferred: "tensa",
                expected: "ten",
              },
            },
          ],
          note: "The '-sa' suffix originated from early language development and helps avoid difficult consonant combinations in speech.",
        },
        irregularVerbs: {
          title: "Regular Verb Forms",
          description:
            "New English uses regular '-it' endings for past tense instead of irregular forms, creating consistent conjugation patterns.",
          tableHeaders: {
            english: "English",
            newEnglish: "New English",
            common: "Common",
            past: "Past",
            pastParticiple: "Past Participle",
          },
          examples: [
            {
              english: {
                common: "cut",
                past: "cut",
                pastParticiple: "cut",
              },
              newEnglish: {
                common: "kat",
                past: "katit",
              },
            },
            {
              english: {
                common: "speak",
                past: "spoke",
                pastParticiple: "spoken",
              },
              newEnglish: {
                common: "spik",
                past: "spikit",
              },
            },
            {
              english: {
                common: "know",
                past: "knew",
                pastParticiple: "known",
              },
              newEnglish: {
                common: "nō",
                past: "nōit",
              },
            },
            {
              english: {
                common: "bring",
                past: "brought",
                pastParticiple: "brought",
              },
              newEnglish: {
                common: "brin",
                past: "brinit",
              },
            },
            {
              english: {
                common: "see",
                past: "saw",
                pastParticiple: "seen",
              },
              newEnglish: {
                common: "si",
                past: "sit",
                note: "double letter cannot be preserved because of language mechanics",
              },
            },
          ],
        },
      },
    },

    ru: {
      _page: {
        title: "Новый английский — Коммуна",
      },
      title: "Новый английский: фонетическая революция",
      problem: {
        title: "Проблема",
        description:
          "Правописание и произношение в английском языке значительно разошлись за века, создавая множество трудностей.",
        difficulties: {
          description: "Современный английский язык создаёт ряд затруднений",
          list: [
            "Существенное расхождение между письменной и устной формами",
            "Непоследовательные правила произношения с множеством исключений",
            "Сложные акценты, сильно различающиеся по регионам",
            "Сложные правила орфографии, не соответствующие произношению",
            "Естественные сокращения в устной речи не отражаются на письме",
            "Избыточные элементы, такие как разные способы обозначения одного и того же звука",
          ],
        },
      },
      idea: {
        title: "Ранние попытки",
        description: {
          1: "Новый английский: уроки ранних трудностей фонетической реформы",
          2: "Наш путь начался с простого вопроса: «Как английский может развиваться со временем?» Первая попытка создать Новый английский столкнулась с серьёзными трудностями из-за чрезмерных упрощений, которые в итоге подорвали целостность системы.",
          3: "Ранняя версия опиралась на произношения онлайн-переводчиков вместо признанных словарей. Такой подход оказался проблематичным, поскольку не имел лингвистической базы для создания последовательных правил. В результате переводы были нестабильны, непоследовательны между платформами и не поддавались автоматизации.",
        },
      },
      implementation: {
        title: "Наша реализация",
        description: "Системный подход к реформе английской орфографии и произношения.",
        features: {
          description: "Особенности нашей реализации Нового английского",
          list: [
            "Слова пишутся точно так, как произносятся",
            "Неопределённые артикли сведены к их сути",
            "Определённые артикли и указательные слова упрощены до «da»",
            "Последовательное фонетическое обозначение всех звуков",
            "Исключение немых букв и избыточных написаний",
            "Сохранение узнаваемости слов при улучшении логической согласованности",
          ],
        },
      },
      dictionaries: {
        title: "Справочные словари",
        description:
          "Наша система основывается на признанных фонетических стандартах из этих авторитетных источников",
        cambridge: {
          name: "Кембриджский словарь английского языка",
          description: "Обширный словарь английского языка с фонетическими транскрипциями",
        },
        opendict: {
          name: "OpenDict",
          description: "Свободно-лицензированные данные словаря",
        },
      },
      sources: {
        title: "Исследовательские источники",
        description: "Наш подход основан на следующих лингвистических ресурсах",
        englishPhonology: {
          name: "Фонология английского языка — Википедия",
          description: "Обзор звуковой системы английского языка",
        },
        internationalPhoneticAlphabet: {
          name: "Международный фонетический алфавит",
          description: "Стандартизированное представление звуков в письменной форме",
        },
        americanIpaChart: {
          name: "IPA-таблица американского английского",
          description: "Таблица фонем американского английского",
        },
      },
      dialects: {
        title: "Базовые диалекты",
        description: "Наша фонетическая система отдаёт приоритет этим диалектам в порядке убывания",
        generalAmerican: {
          name: "Общий американский",
          description: "Акцент американского английского, воспринимаемый как нейтральный",
        },
        standardEnglish: {
          name: "Стандартный английский",
          description: "Стандартный акцент британского английского",
        },
        localDialect: {
          name: "Местный диалект",
          description: "Форма английского языка, используемая в конкретной местности",
        },
      },
      alphabet: {
        title: "Алфавит Нового английского",
      },
      rules: {
        title: "Фонетические правила",
        description: "Каждый звук последовательно обозначается определённой буквой или буквами",
        phoneticSounds: "Фонетические звуки",
        newEnglishLetter: "Буквы Нового английского",
        exampleWords: "Примеры слов",
      },
      nuances: {
        title: "Особенности языка",
        description:
          "Новый английский включает несколько практических изменений для улучшения согласованности и удобства произношения",
        pronoun: {
          title: "Личные местоимения",
          description:
            "Новый английский использует 'Me' вместо 'I' для согласованности с другими формами местоимений и устранения произвольного правила заглавных букв.",
        },
        interrogatives: {
          title: "Вопросительные слова",
          description:
            "Вопросительные слова часто используют суффикс '-sa' для избежания скопления согласных и улучшения произношения:",
          tableHeaders: {
            original: "Оригинал",
            preferred: "Предпочтительно",
            expected: "Ожидаемо",
          },
          examples: [
            {
              before: "what",
              after: {
                preferred: "wotsa",
                expected: "wot",
              },
            },
            {
              before: "who",
              after: {
                preferred: "hysa",
                expected: "hy",
              },
            },
            {
              before: "when",
              after: {
                preferred: "wensa",
                expected: "wen",
              },
            },
            {
              before: "where",
              after: {
                preferred: "wersa",
                expected: "wer",
              },
            },
            {
              before: "which",
              after: {
                preferred: "wičsa",
                expected: "wič",
              },
            },
            {
              before: "why",
              after: {
                preferred: "waísa",
                expected: "waí",
              },
            },
            {
              before: "how",
              after: {
                preferred: "haysa",
                expected: "hay",
              },
            },

            {
              before: "there",
              after: {
                preferred: "tersa",
                expected: "ter",
              },
            },
            {
              before: "then",
              after: {
                preferred: "tensa",
                expected: "ten",
              },
            },
          ],
          note: "Суффикс '-sa' возник из ранней разработки языка и помогает избежать сложных сочетаний согласных в речи.",
        },
        irregularVerbs: {
          title: "Правильные формы глаголов",
          description:
            "Новый английский использует регулярные окончания '-it' для прошедшего времени вместо неправильных форм, создавая последовательные модели спряжения.",
          tableHeaders: {
            english: "Английский",
            newEnglish: "Новый английский",
            common: "Обычная форма",
            past: "Прошедшее время",
            pastParticiple: "Причастие прошедшего времени",
          },
          examples: [
            {
              english: {
                common: "cut",
                past: "cut",
                pastParticiple: "cut",
              },
              newEnglish: {
                common: "kat",
                past: "katit",
              },
            },
            {
              english: {
                common: "speak",
                past: "spoke",
                pastParticiple: "spoken",
              },
              newEnglish: {
                common: "spik",
                past: "spikit",
              },
            },
            {
              english: {
                common: "know",
                past: "knew",
                pastParticiple: "known",
              },
              newEnglish: {
                common: "nō",
                past: "nōit",
              },
            },
            {
              english: {
                common: "bring",
                past: "brought",
                pastParticiple: "brought",
              },
              newEnglish: {
                common: "brin",
                past: "brinit",
              },
            },
            {
              english: {
                common: "see",
                past: "saw",
                pastParticiple: "seen",
              },
              newEnglish: {
                common: "si",
                past: "sit",
                note: "сдвоенный звук не может быть сохранён из-за механики языка",
              },
            },
          ],
        },
      },
    },
  };

  const { data } = $props();
  const { locale } = $derived(data);

  const t = $derived(i18n[locale]);
</script>

{#snippet dictionaryItem(key: DictionaryKey, name: string, description: string, url: string)}
  <li class="list-group-item" style:border-left={`4px solid #${lightDictionaryColors[key]}`}>
    <strong>
      <a href={url} target="_blank" rel="noopener noreferrer">
        {name}
      </a>
    </strong>
    <p class="mb-0 text-muted">{description}</p>
  </li>
{/snippet}

{#snippet sourceItem(name: string, description: string, url: string)}
  <li class="list-group-item">
    <strong>
      <a href={url} target="_blank" rel="noopener noreferrer">
        {name}
      </a>
    </strong>
    <p class="mb-0 text-muted">{description}</p>
  </li>
{/snippet}

{#snippet dialectItem(name: string, description: string, priority: number)}
  <li class="list-group-item">
    <div class="d-flex align-items-center">
      <span class="badge bg-primary me-2">{priority}</span>
      <div>
        <strong>{name}</strong>
        <p class="mb-0 text-muted">{description}</p>
      </div>
    </div>
  </li>
{/snippet}

{#snippet highlightedExample(example: string)}
  {@html (() => {
    const [before, middle, after] = example.split("*") as [string, string, string];

    return [
      `<span>${before}</span>`,
      `<strong style="color:#d63384">${middle}</strong>`,
      `<span>${after}</span>`,
    ].join("");
  })()}
{/snippet}

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-5">
  <div class="responsive-container">
    <h1 class="mb-4">{t.title}</h1>

    <!-- Problem Section -->
    <section class="mb-5">
      <h2>{t.problem.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.problem.description}</p>
          <p>{t.problem.difficulties.description}:</p>
          <ul>
            {#each t.problem.difficulties.list as difficulty}
              <li>{difficulty}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Idea Section -->
    <section class="mb-5">
      <h2>{t.idea.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.idea.description[1]}</p>
          <p>{t.idea.description[2]}</p>
          <p>{t.idea.description[3]}</p>
        </div>
      </div>
    </section>

    <!-- Implementation Section -->
    <section class="mb-5">
      <h2>{t.implementation.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.implementation.description}</p>
          <p>{t.implementation.features.description}:</p>
          <ul>
            {#each t.implementation.features.list as feature}
              <li>{feature}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Dictionaries Section -->
    <section class="mb-5">
      <h2>{t.dictionaries.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.dictionaries.description}:</p>
          <ul class="list-group list-group-flush">
            {@render dictionaryItem(
              "cambridge",
              t.dictionaries.cambridge.name,
              t.dictionaries.cambridge.description,
              "https://dictionary.cambridge.org",
            )}

            {@render dictionaryItem(
              "opendict",
              t.dictionaries.opendict.name,
              t.dictionaries.opendict.description,
              "https://open-dict-data.github.io",
            )}
          </ul>
        </div>
      </div>
    </section>

    <!-- Sources Section -->
    <section class="mb-5">
      <h2>{t.sources.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.sources.description}:</p>
          <ul class="list-group list-group-flush">
            {@render sourceItem(
              t.sources.englishPhonology.name,
              t.sources.englishPhonology.description,
              "https://en.wikipedia.org/wiki/English_phonology",
            )}

            {@render sourceItem(
              t.sources.internationalPhoneticAlphabet.name,
              t.sources.internationalPhoneticAlphabet.description,
              "https://www.internationalphoneticalphabet.org/",
            )}

            {@render sourceItem(
              t.sources.americanIpaChart.name,
              t.sources.americanIpaChart.description,
              "https://americanipachart.com/",
            )}
          </ul>
        </div>
      </div>
    </section>

    <!-- Dialects Section -->
    <section class="mb-5">
      <h2>{t.dialects.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.dialects.description}:</p>
          <ul class="list-group list-group-flush">
            {@render dialectItem(
              t.dialects.generalAmerican.name,
              t.dialects.generalAmerican.description,
              1,
            )}

            {@render dialectItem(
              t.dialects.standardEnglish.name,
              t.dialects.standardEnglish.description,
              2,
            )}

            {@render dialectItem(
              t.dialects.localDialect.name,
              t.dialects.localDialect.description,
              3,
            )}
          </ul>
        </div>
      </div>
    </section>

    <!-- Alphabet Section -->
    <section class="mb-5">
      <h2>{t.alphabet.title}</h2>
      <div class="card">
        <div class="card-body">
          <div class="row row-cols-2 row-cols-md-3 row-cols-lg-6 g-2">
            {#each alphabet as item}
              <div class="col">
                <div class="card h-100">
                  <div class="card-body text-center">
                    <h3 class="card-title">{item}</h3>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      </div>
    </section>

    <!-- Rules Section -->
    <section class="mb-5">
      <h2>{t.rules.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.rules.description}:</p>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>{t.rules.phoneticSounds}</th>
                  <th>{t.rules.newEnglishLetter}</th>
                  <th>{t.rules.exampleWords}</th>
                </tr>
              </thead>
              <tbody>
                {#each rules as rule}
                  <tr>
                    <td>
                      {#each rule.sources as source}
                        <div class="mb-1">
                          {#each source.sounds as sound}
                            <span
                              class="badge rounded-pill me-2 font-freemono"
                              style:background-color={`#${lightDictionaryColors[source.dictionaryKey]}`}
                              style:color="#000000"
                              style:padding="6px 12px"
                              style:margin-bottom="6px"
                              style:font-size="1.2rem"
                              style:font-weight="normal"
                            >
                              {sound}
                            </span>
                          {/each}
                        </div>
                      {/each}
                    </td>
                    <td>
                      <strong
                        style:font-size="1.5rem"
                        style:display="inline-block"
                        style:padding="4px 0">{rule.mapping}</strong
                      >
                    </td>
                    <td>
                      {#each rule.examples as example, i}
                        <span>
                          {#if i > 0}
                            ,
                          {/if}

                          {@render highlightedExample(example)}
                        </span>
                      {/each}
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>

    <ExamplesSection {locale} />

    <!-- Nuances Section -->
    <section class="mb-5">
      <h2>{t.nuances.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.nuances.description}:</p>

          <div class="mb-4">
            <h4 class="h5">{t.nuances.pronoun.title}</h4>
            <p>{t.nuances.pronoun.description}</p>
          </div>

          <div class="mb-4">
            <h4 class="h5">{t.nuances.interrogatives.title}</h4>
            <p>{t.nuances.interrogatives.description}</p>
            <div class="d-inline-block">
              <table class="table table-sm table-striped" style="width: auto; min-width: 400px;">
                <thead>
                  <tr>
                    <th>{t.nuances.interrogatives.tableHeaders.original}</th>
                    <th>{t.nuances.interrogatives.tableHeaders.preferred}</th>
                    <th>{t.nuances.interrogatives.tableHeaders.expected}</th>
                  </tr>
                </thead>
                <tbody>
                  {#each t.nuances.interrogatives.examples as example}
                    <tr>
                      <td><code class="text-dark">{example.before}</code></td>
                      <td><code class="text-primary fw-bold">{example.after.preferred}</code></td>
                      <td><code class="text-secondary">{example.after.expected}</code></td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
            <div class="alert alert-info mt-2">
              <small>
                <strong>Note:</strong>
                {t.nuances.interrogatives.note}
              </small>
            </div>
          </div>

          <div class="mb-4">
            <h4 class="h5">{t.nuances.irregularVerbs.title}</h4>
            <p>{t.nuances.irregularVerbs.description}</p>
            <div class="table-responsive">
              <table class="table table-sm table-striped">
                <thead>
                  <tr>
                    <th colspan="3" class="text-center bg-light"
                      >{t.nuances.irregularVerbs.tableHeaders.english}</th
                    >
                    <th colspan="2" class="text-center bg-primary text-white"
                      >{t.nuances.irregularVerbs.tableHeaders.newEnglish}</th
                    >
                  </tr>
                  <tr>
                    <th>{t.nuances.irregularVerbs.tableHeaders.common}</th>
                    <th>{t.nuances.irregularVerbs.tableHeaders.past}</th>
                    <th>{t.nuances.irregularVerbs.tableHeaders.pastParticiple}</th>
                    <th>{t.nuances.irregularVerbs.tableHeaders.common}</th>
                    <th>{t.nuances.irregularVerbs.tableHeaders.past}</th>
                  </tr>
                </thead>
                <tbody>
                  {#each t.nuances.irregularVerbs.examples as example}
                    <tr>
                      <td><code class="text-muted">{example.english.common}</code></td>
                      <td><code class="text-muted">{example.english.past}</code></td>
                      <td><code class="text-muted">{example.english.pastParticiple}</code></td>
                      <td><code class="text-primary fw-bold">{example.newEnglish.common}</code></td>
                      <td>
                        <code class="text-primary fw-bold">{example.newEnglish.past}</code>
                        {#if example.newEnglish.past === "sit" && example.newEnglish.note}
                          <span
                            class="ms-1 text-info"
                            style="cursor: help;"
                            title={example.newEnglish.note}
                          >
                            <i class="bi bi-question-circle"></i>
                          </span>
                        {/if}
                      </td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
