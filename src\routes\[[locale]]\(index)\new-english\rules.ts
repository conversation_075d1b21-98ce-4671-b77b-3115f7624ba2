import type { DictionaryKeyOrAny } from "./dictionaries";

export const rules: {
  sources: {
    dictionaryKey: DictionaryKeyOrAny;
    sounds: string[];
  }[];
  mapping: string;
  examples: string[];
}[] = [
  /*
    [a, ɑ, /ɔ, ʌ, ə$] - a
    aɪ - aí
    aʊ - ay
    æ - ā
    b - b
    bə - bi
  */

  {
    sources: [
      {
        dictionaryKey: "opendict",
        sounds: ["ɔ"],
      },
      {
        dictionaryKey: "any",
        sounds: ["a", "ɑ", "ʌ", "ə$"],
      },
    ],
    mapping: "a",
    examples: ["d*u*st", "b*u*lk"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["aɪ"],
      },
    ],
    mapping: "aí",
    examples: ["l*i*me", "d*i*nosaur"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["aʊ"],
      },
    ],
    mapping: "ay",
    examples: ["m*ou*se", "br*ow*n"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["æ"],
      },
    ],
    mapping: "ā",
    examples: ["r*a*t", "c*a*t"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["b"],
      },
    ],
    mapping: "b",
    examples: ["*b*ear", "ze*b*ra"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["bə"],
      },
    ],
    mapping: "bi",
    examples: ["*be*aver", "rab*bi*t"],
  },

  /*
    tʃ - č
    d - d
    də - da
    [e, /ɛ, ə, ɚ/ɝ$] - e
    eɪ - eí
    ɝ - ë
  */

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["tʃ"],
      },
    ],
    mapping: "č",
    examples: ["approa*ch*", "*ch*icken"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["d"],
      },
    ],
    mapping: "d",
    examples: ["*d*og", "*d*inosaur"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["də"],
      },
    ],
    mapping: "da",
    examples: ["*du*st", "aque*du*ct"],
  },

  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["ɚ$"],
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɛ", "ɝ$"],
      },
      {
        dictionaryKey: "any",
        sounds: ["e", "ə"],
      },
    ],
    mapping: "e",
    examples: ["r*e*d", "b*e*ar"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["eɪ"],
      },
    ],
    mapping: "eí",
    examples: ["sn*a*ke", "sh*a*pe"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ɝ"],
      },
    ],
    mapping: "ë",
    examples: ["t*ur*tle", "p*ur*ple"],
  },

  /*
    [f, θ, v$] - f
    ɡ - g
    h - h
    [i, ɪ] - i
    ɪˈr/ɝˈ - ir
    əs$ - is
  */

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["f", "θ", "v$"],
      },
    ],
    mapping: "f",
    examples: ["*f*rog", "*f*eather"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ɡ"],
      },
    ],
    mapping: "g",
    examples: ["*g*oat", "*g*reen"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["h"],
      },
    ],
    mapping: "h",
    examples: ["*h*orse", "*h*ouse"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["i", "ɪ"],
      },
    ],
    mapping: "i",
    examples: ["p*i*g", "z*e*bra"],
  },

  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["ɪˈr"],
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɝˈ"],
      },
    ],
    mapping: "ir",
    examples: ["g*ir*affe", "b*ur*rito"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["əs$"],
      },
    ],
    mapping: "is",
    examples: ["vers*us*"],
  },

  /*
    j - í
    ʒ - j
    [k, ɡ$] - k
    [l/ɫ, əl/əɫ$] - l
    lə - le
    m - m
  */

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["j"],
      },
    ],
    mapping: "í",
    examples: ["*y*ak", "*y*acht"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ʒ"],
      },
    ],
    mapping: "j",
    examples: ["televi*sio*n", "*j*ade"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["k", "ɡ$"],
      },
    ],
    mapping: "k",
    examples: ["pin*k*", "*c*offee"],
  },

  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["l", "əl$"],
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɫ", "əɫ$"],
      },
    ],
    mapping: "l",
    examples: ["*l*ion", "go*l*d"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["lə"],
      },
    ],
    mapping: "le",
    examples: ["te*le*vision"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["m"],
      },
    ],
    mapping: "m",
    examples: ["*m*auve", "*m*ouse"],
  },

  /*
    [n, ŋ, ən, ɪn/$] - n
    nə - na
    [ʊ, ɔːr/ɔɹ] - o
    ɔɪ - oí
    oʊ - ō
    [p, b$] - p
  */

  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["ɪn$"],
      },
      {
        dictionaryKey: "any",
        sounds: ["n", "ŋ", "ən"],
      },
    ],
    mapping: "n",
    examples: ["brow*n*", "sa*n*d"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["nə"],
      },
    ],
    mapping: "na",
    examples: ["di*no*saur"],
  },

  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["ɔːr"],
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɔɹ"],
      },
      {
        dictionaryKey: "any",
        sounds: ["ʊ"],
      },
    ],
    mapping: "o",
    examples: ["w*o*lf", "h*o*rse"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ɔɪ"],
      },
    ],
    mapping: "oí",
    examples: ["turq*uoi*se"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["oʊ"],
      },
    ],
    mapping: "ō",
    examples: ["g*oa*t", "appr*oa*ch"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["p", "b$"],
      },
    ],
    mapping: "p",
    examples: ["*p*ig", "*p*enguin"],
  },

  /*
    r/ɹ - r
    rə - ra
    r/ɹ$ - *
    [s, z$] - s
    ʃ - š
    [t, t̬/, d$] - t
  */

  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["r"],
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɹ"],
      },
    ],
    mapping: "r",
    examples: ["*r*ed", "f*r*og"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["rə"],
      },
    ],
    mapping: "ra",
    examples: ["zeb*ra*"],
  },

  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["r$"],
      },
      {
        dictionaryKey: "opendict",
        sounds: ["ɹ$"],
      },
    ],
    mapping: "",
    examples: ["bea*r*"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["s", "z$"],
      },
    ],
    mapping: "s",
    examples: ["*s*nake", "*s*un"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["ʃ"],
      },
    ],
    mapping: "š",
    examples: ["*sh*eep", "*sh*epherd"],
  },

  {
    sources: [
      {
        dictionaryKey: "cambridge",
        sounds: ["t̬"],
      },
      {
        dictionaryKey: "any",
        sounds: ["t", "d$"],
      },
    ],
    mapping: "t",
    examples: ["re*d*", "goa*t*"],
  },

  /*
    [u, jə] - u
    [v, ð] - v
    w - w
    wə - we
    z - z
  */

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["u", "jə"],
      },
    ],
    mapping: "u",
    examples: ["bl*ue*"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["v", "ð"],
      },
    ],
    mapping: "v",
    examples: ["fea*th*er", "bea*v*er"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["w"],
      },
    ],
    mapping: "w",
    examples: ["*w*olf", "*w*ood"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["wə"],
      },
    ],
    mapping: "we",
    examples: ["aq*ue*duct"],
  },

  {
    sources: [
      {
        dictionaryKey: "any",
        sounds: ["z"],
      },
    ],
    mapping: "z",
    examples: ["*z*ebra", "*z*oo"],
  },

  // {
  //   sources: [
  //     {
  //       dictionaryKey: "",
  //       sounds: [],
  //     },
  //   ],
  //   mapping: "",
  //   examples: [],
  // },
];
