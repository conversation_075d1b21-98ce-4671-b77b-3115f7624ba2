FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json .
RUN npm ci
COPY svelte.config.js vite.config.ts tsconfig.json ./
COPY /static ./static
COPY /src ./src
RUN npm run build
RUN npm prune --production

FROM node:18-alpine
WORKDIR /app
COPY server.js .
COPY --from=builder /app/build build/
COPY --from=builder /app/node_modules node_modules/
COPY package.json .
ENV NODE_ENV=production
CMD [ "node", "server.js" ]
