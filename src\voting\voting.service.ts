import { Injectable } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { toPrismaPagination } from "src/utils";
import { CurrentUser } from "src/auth/types";
import { Localization } from "src/zod/helper";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";

type CreateDto = {
    votesRequired: number;
    endsAt: Date;
    title: Localization[];
    description: Localization[];
    options: {
        title: Localization[];
    }[];
};

@Injectable()
export class VotingService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super("voting");
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        return true;
    }

    async check(ids: string[]) {
        return await this._check(ids, this.prisma.voting, "voting");
    }

    async getOne(id: string) {
        return await this.prisma.voting.findUnique({
            where: { id, deletedAt: null },

            include: {
                title: true,
                description: true,

                options: {
                    include: {
                        title: true,
                    },
                },
            },
        });
    }

    async getOneOrThrow(id: string) {
        const voting = await this.getOne(id);

        if (!voting) {
            throw this.createNotFoundException();
        }

        return voting;
    }

    async getMany(
        where: Prisma.VotingWhereInput,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.voting.findMany({
            ...toPrismaPagination(pagination),

            where: {
                ...where,
                deletedAt: null,
            },

            include: {
                title: true,
                description: true,

                options: {
                    include: {
                        title: true,
                    },
                },
            },
        });
    }

    async createOne(data: Prisma.VotingCreateInput) {
        return await this.prisma.voting.create({ data });
    }

    async createMany(data: Prisma.VotingCreateManyInput[]) {
        return await this.prisma.voting.createMany({ data });
    }

    async create(data: CreateDto) {
        return await this.prisma.voting.create({
            data: {
                votesRequired: data.votesRequired,
                endsAt: data.endsAt,

                title: {
                    create: data.title.map((item) => ({
                        ...item,
                        key: "title",
                    })),
                },

                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },

                options: {
                    create: data.options.map((option) => ({
                        title: {
                            create: option.title,
                        },
                    })),
                },
            },

            include: {
                title: true,
                description: true,

                options: {
                    include: {
                        title: true,
                    },
                },
            },
        });
    }

    async updateOne(id: string, data: Prisma.VotingUpdateInput) {
        return await this.prisma.voting.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }

    async updateMany(
        where: Prisma.VotingWhereInput,
        data: Prisma.VotingUpdateInput,
    ) {
        return await this.prisma.voting.updateMany({ where, data });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteMany(where: Prisma.VotingWhereInput) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }

    async deleteOne(id: string) {
        return await this.prisma.voting.delete({ where: { id } });
    }

    async deleteMany(where: Prisma.VotingWhereInput) {
        return await this.prisma.voting.deleteMany({ where });
    }
}
