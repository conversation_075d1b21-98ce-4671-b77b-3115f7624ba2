import { Module } from "@nestjs/common";
import { CommuneService } from "./commune.service";
import { CommuneController } from "./http/commune.controller";
import { CommuneMemberService } from "./commune-member.service";
import { UserModule } from "src/user/user.module";
import { MinioModule } from "src/minio/minio.module";

@Module({
    imports: [UserModule, MinioModule],
    controllers: [CommuneController],
    providers: [CommuneService, CommuneMemberService],
    exports: [CommuneService, CommuneMemberService],
})
export class CommuneModule {}
