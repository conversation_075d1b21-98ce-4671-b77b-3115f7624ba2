import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
import { UserRole } from "@prisma/client";

export const color = z.string().nonempty().nullable();
export const isActive = z.boolean();

export type User = ZodHelper.Infer<typeof User>;
export const User = z.object({
    id: Zod<PERSON>elper.Uuid,
    email: ZodHelper.Email,
    role: z.nativeEnum(UserRole),
    name: ZodHelper.Localizations,
    description: ZodHelper.Localizations,
    images: z.array(ZodHelper.Image).optional(),
    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const Users = z.array(User);

export type UpdateUser = ZodHelper.Infer<typeof UpdateUser>;
export const UpdateUser = z
    .object({
        name: ZodHelper.Localizations,
        description: ZodHelper.Localizations,
    })
    .partial();

export type UserTitle = ZodHelper.Infer<typeof UserTitle>;
export const UserTitle = z.object({
    id: ZodHelper.Uuid,
    ownerId: ZodHelper.Uuid.nullable(),
    isActive,
    color,
    createdAt: <PERSON>odHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const UserTitles = z.array(UserTitle);

export type UpdateUserTitle = ZodHelper.Infer<typeof UpdateUserTitle>;
export const UpdateUserTitle = z
    .object({
        color,
        isActive,
    })
    .partial();
