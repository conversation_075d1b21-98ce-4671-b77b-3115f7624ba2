-- CreateEnum
CREATE TYPE "commune_member_type" AS ENUM ('commune', 'user');

-- CreateEnum
CREATE TYPE "locale" AS ENUM ('en', 'ru');

-- CreateEnum
CREATE TYPE "merchandise_status" AS ENUM ('hidden', 'shown');

-- CreateEnum
CREATE TYPE "post_status" AS ENUM ('draft', 'published', 'archived');

-- CreateEnum
CREATE TYPE "user_role" AS ENUM ('admin', 'moderator', 'user');

-- CreateEnum
CREATE TYPE "vote_actor_type" AS ENUM ('commune', 'user');

-- CreateTable
CREATE TABLE "communes" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "communes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "commune_members" (
    "id" UUID NOT NULL,
    "commune_id" UUID NOT NULL,
    "actorType" "commune_member_type" NOT NULL,
    "actor_id" UUID NOT NULL,
    "isHead" BOOLEAN NOT NULL DEFAULT false,
    "joined_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "left_at" TIMESTAMPTZ(3),
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "commune_members_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "images" (
    "id" UUID NOT NULL,
    "url" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "localizations" (
    "id" UUID NOT NULL,
    "key" TEXT NOT NULL,
    "locale" "locale" NOT NULL,
    "value" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "localizations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "merchandises" (
    "id" UUID NOT NULL,
    "category" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "status" "merchandise_status" NOT NULL DEFAULT 'hidden',
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "merchandises_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "posts" (
    "id" UUID NOT NULL,
    "status" "post_status" NOT NULL DEFAULT 'draft',
    "published_at" TIMESTAMPTZ(3),
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "posts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tags" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "tags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" UUID NOT NULL,
    "referrer_id" UUID,
    "email" TEXT NOT NULL,
    "role" "user_role" NOT NULL DEFAULT 'user',
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_titles" (
    "id" UUID NOT NULL,
    "owner_id" UUID,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "color" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "user_titles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_refresh_tokens" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "token" TEXT NOT NULL,
    "expires_at" TIMESTAMPTZ(3) NOT NULL,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "revoked_at" TIMESTAMPTZ(3),
    "revoke_reason" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "user_refresh_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_otps" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "otp" TEXT NOT NULL,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "expires_at" TIMESTAMPTZ(3) NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "user_otps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "votings" (
    "id" UUID NOT NULL,
    "votes_required" INTEGER NOT NULL,
    "ends_at" TIMESTAMPTZ(3) NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "votings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "voting_options" (
    "id" UUID NOT NULL,
    "voting_id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "voting_options_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "votes" (
    "id" UUID NOT NULL,
    "actorType" "vote_actor_type" NOT NULL,
    "actor_id" UUID NOT NULL,
    "voting_id" UUID,
    "option_id" UUID,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "votes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_commune_images" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_commune_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_commune_tags" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_commune_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_commune_name" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_commune_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_commune_description" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_commune_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_images" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_user_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_images" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_voting_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_post_images" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_post_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_merchandise_images" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_merchandise_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_name" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_user_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_description" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_user_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_title" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_voting_title_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_description" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_voting_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_option_title" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_voting_option_title_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_option_description" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_voting_option_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_post_title" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_post_title_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_post_description" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_post_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_merchandise_tags" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_merchandise_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_post_tags" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_post_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_tags" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_voting_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "merchandises_category_idx" ON "merchandises"("category");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE INDEX "_commune_images_B_index" ON "_commune_images"("B");

-- CreateIndex
CREATE INDEX "_commune_tags_B_index" ON "_commune_tags"("B");

-- CreateIndex
CREATE INDEX "_commune_name_B_index" ON "_commune_name"("B");

-- CreateIndex
CREATE INDEX "_commune_description_B_index" ON "_commune_description"("B");

-- CreateIndex
CREATE INDEX "_user_images_B_index" ON "_user_images"("B");

-- CreateIndex
CREATE INDEX "_voting_images_B_index" ON "_voting_images"("B");

-- CreateIndex
CREATE INDEX "_post_images_B_index" ON "_post_images"("B");

-- CreateIndex
CREATE INDEX "_merchandise_images_B_index" ON "_merchandise_images"("B");

-- CreateIndex
CREATE INDEX "_user_name_B_index" ON "_user_name"("B");

-- CreateIndex
CREATE INDEX "_user_description_B_index" ON "_user_description"("B");

-- CreateIndex
CREATE INDEX "_voting_title_B_index" ON "_voting_title"("B");

-- CreateIndex
CREATE INDEX "_voting_description_B_index" ON "_voting_description"("B");

-- CreateIndex
CREATE INDEX "_voting_option_title_B_index" ON "_voting_option_title"("B");

-- CreateIndex
CREATE INDEX "_voting_option_description_B_index" ON "_voting_option_description"("B");

-- CreateIndex
CREATE INDEX "_post_title_B_index" ON "_post_title"("B");

-- CreateIndex
CREATE INDEX "_post_description_B_index" ON "_post_description"("B");

-- CreateIndex
CREATE INDEX "_merchandise_tags_B_index" ON "_merchandise_tags"("B");

-- CreateIndex
CREATE INDEX "_post_tags_B_index" ON "_post_tags"("B");

-- CreateIndex
CREATE INDEX "_voting_tags_B_index" ON "_voting_tags"("B");

-- AddForeignKey
ALTER TABLE "commune_members" ADD CONSTRAINT "commune_members_commune_id_fkey" FOREIGN KEY ("commune_id") REFERENCES "communes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_referrer_id_fkey" FOREIGN KEY ("referrer_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_titles" ADD CONSTRAINT "user_titles_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_refresh_tokens" ADD CONSTRAINT "user_refresh_tokens_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "voting_options" ADD CONSTRAINT "voting_options_voting_id_fkey" FOREIGN KEY ("voting_id") REFERENCES "votings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "votes" ADD CONSTRAINT "votes_voting_id_fkey" FOREIGN KEY ("voting_id") REFERENCES "votings"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "votes" ADD CONSTRAINT "votes_option_id_fkey" FOREIGN KEY ("option_id") REFERENCES "voting_options"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_images" ADD CONSTRAINT "_commune_images_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_images" ADD CONSTRAINT "_commune_images_B_fkey" FOREIGN KEY ("B") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_tags" ADD CONSTRAINT "_commune_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_tags" ADD CONSTRAINT "_commune_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_name" ADD CONSTRAINT "_commune_name_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_name" ADD CONSTRAINT "_commune_name_B_fkey" FOREIGN KEY ("B") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_description" ADD CONSTRAINT "_commune_description_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_description" ADD CONSTRAINT "_commune_description_B_fkey" FOREIGN KEY ("B") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_images" ADD CONSTRAINT "_user_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_images" ADD CONSTRAINT "_user_images_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_images" ADD CONSTRAINT "_voting_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_images" ADD CONSTRAINT "_voting_images_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_images" ADD CONSTRAINT "_post_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_images" ADD CONSTRAINT "_post_images_B_fkey" FOREIGN KEY ("B") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_images" ADD CONSTRAINT "_merchandise_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_images" ADD CONSTRAINT "_merchandise_images_B_fkey" FOREIGN KEY ("B") REFERENCES "merchandises"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_name" ADD CONSTRAINT "_user_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_name" ADD CONSTRAINT "_user_name_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_description" ADD CONSTRAINT "_user_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_description" ADD CONSTRAINT "_user_description_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_title" ADD CONSTRAINT "_voting_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_title" ADD CONSTRAINT "_voting_title_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_description" ADD CONSTRAINT "_voting_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_description" ADD CONSTRAINT "_voting_description_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_title" ADD CONSTRAINT "_voting_option_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_title" ADD CONSTRAINT "_voting_option_title_B_fkey" FOREIGN KEY ("B") REFERENCES "voting_options"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_description" ADD CONSTRAINT "_voting_option_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_description" ADD CONSTRAINT "_voting_option_description_B_fkey" FOREIGN KEY ("B") REFERENCES "voting_options"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_title" ADD CONSTRAINT "_post_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_title" ADD CONSTRAINT "_post_title_B_fkey" FOREIGN KEY ("B") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_description" ADD CONSTRAINT "_post_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_description" ADD CONSTRAINT "_post_description_B_fkey" FOREIGN KEY ("B") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_tags" ADD CONSTRAINT "_merchandise_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "merchandises"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_tags" ADD CONSTRAINT "_merchandise_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_tags" ADD CONSTRAINT "_post_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_post_tags" ADD CONSTRAINT "_post_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_tags" ADD CONSTRAINT "_voting_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_tags" ADD CONSTRAINT "_voting_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;
