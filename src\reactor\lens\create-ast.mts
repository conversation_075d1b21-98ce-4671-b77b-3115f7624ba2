/* eslint-disable prefer-const */

import { IToken } from "chevrotain";

/**
 * Represents a logical AND operation containing multiple statements.
 * All statements must be true for the AND operation to be true.
 */
export type And = {
    type: "and";
    statements: Statement[];
};

/**
 * Represents a logical OR operation containing multiple statements.
 * At least one statement must be true for the OR operation to be true.
 */
export type Or = {
    type: "or";
    statements: Statement[];
};

/**
 * Supported comparison operators for field comparisons.
 * - "=" : equals
 * - "!=" : not equals
 * - ">" : greater than
 * - ">=" : greater than or equal
 * - "<" : less than
 * - "<=" : less than or equal
 * - "~" : contains/matches (for text search)
 */
export type ComparisonOperator = "=" | "!=" | ">" | ">=" | "<" | "<=" | "~";

/**
 * Represents a comparison operation between a field and a value.
 * Examples: hub = ["guid1", "guid2"], rating >= 200, title ~ "python"
 */
export type Comparison = {
    type: "comparison";
    identifier: string;
    operator: ComparisonOperator;
    value: string | number | (string | number)[];
};

/**
 * Union type representing any valid statement in the query language.
 * Can be a logical operation (And/Or) or a field comparison.
 */
export type Statement = And | Or | Comparison;

/**
 * Parser for a query language that supports logical operations (AND, OR) and field comparisons.
 *
 * The parser implements a recursive descent parser with operator precedence:
 * 1. AND (&&) has the lowest precedence
 * 2. OR (||) has higher precedence than AND
 * 3. Parentheses can override precedence
 * 4. Comparisons have the highest precedence
 *
 * Grammar:
 * ```
 * expression := and_expr
 * and_expr   := or_expr ('&&' or_expr)*
 * or_expr    := term ('||' term)*
 * term       := '(' expression ')' | comparison
 * comparison := IDENTIFIER OPERATOR value
 * value      := '[' literal (',' literal)* ']' | literal
 * literal    := STRING | NUMBER | IDENTIFIER
 * ```
 *
 * Example queries:
 * - `hub = ["guid1", "guid2"] && rating >= 200`
 * - `(title ~ "python" || body ~ "pandas") && difficulty = ["easy", "medium"]`
 * - `age < 3d && usefulness >= 8`
 */
class Parser {
    /** Array of tokens to parse */
    tokens: IToken[];
    /** Current position in the tokens array */
    pos = 0;

    /**
     * Creates a new Parser instance.
     * @param tokens Array of string tokens to parse
     */
    constructor(tokens: IToken[]) {
        this.tokens = tokens;
    }

    /**
     * Peeks at the current token without consuming it.
     * @returns The current token or undefined if at end of input
     */
    peek() {
        return this.tokens[this.pos];
    }

    /**
     * Consumes and returns the current token, advancing the position.
     * @returns The current token or undefined if at end of input
     */
    next() {
        return this.tokens[this.pos++];
    }

    /**
     * Consumes the current token if it matches the expected token, otherwise throws an error.
     * @param token The expected token
     * @throws Error if the current token doesn't match the expected token
     */
    eat(token: string) {
        if (this.peek()?.image !== token) {
            throw new Error(
                `Expected ${token}, got ${this.peek()?.image} at ${this.pos}`,
            );
        }

        this.pos++;
    }

    /**
     * Parses the entire token stream and returns the root AST node.
     * @returns The parsed statement representing the entire expression
     */
    parse(): Statement {
        return this.parseAnd();
    }

    /**
     * Parses AND expressions (lowest precedence level).
     * Handles chains of expressions connected by '&&' operators.
     *
     * @returns A Statement - either an And node if multiple statements are found,
     *          or the single statement if only one is present
     *
     * @example
     * Input: "a = 1 && b = 2 && c = 3"
     * Output: { type: "and", statements: [comparison_a, comparison_b, comparison_c] }
     */
    parseAnd(): Statement {
        let left = this.parseOr();
        const statements = [left];

        while (this.peek()?.image === "&&") {
            this.next(); // consume '&&'
            statements.push(this.parseOr());
        }

        return statements.length > 1 ? { type: "and", statements } : left;
    }

    /**
     * Parses OR expressions (higher precedence than AND).
     * Handles chains of expressions connected by '||' operators.
     *
     * @returns A Statement - either an Or node if multiple statements are found,
     *          or the single statement if only one is present
     *
     * @example
     * Input: "a = 1 || b = 2"
     * Output: { type: "or", statements: [comparison_a, comparison_b] }
     */
    parseOr(): Statement {
        let left = this.parseTerm();
        const statements = [left];

        while (this.peek()?.image === "||") {
            this.next(); // consume '||'
            statements.push(this.parseTerm());
        }

        return statements.length > 1 ? { type: "or", statements } : left;
    }

    /**
     * Parses terms - either parenthesized expressions or comparisons.
     * This handles the highest precedence level and parentheses for grouping.
     *
     * @returns A Statement representing either a grouped expression or a comparison
     *
     * @example
     * Input: "(a = 1 && b = 2)"
     * Output: { type: "and", statements: [comparison_a, comparison_b] }
     *
     * @example
     * Input: "rating >= 200"
     * Output: { type: "comparison", identifier: "rating", operator: ">=", value: 200 }
     */
    parseTerm(): Statement {
        if (this.peek()?.image === "(") {
            this.next(); // consume '('
            const expr = this.parseAnd(); // recursively parse the grouped expression
            this.eat(")"); // ensure closing parenthesis

            return expr;
        }

        return this.parseComparison();
    }

    /**
     * Parses field comparison expressions in the format: field operator value
     *
     * @returns A Comparison statement with identifier, operator, and value
     * @throws Error if the field name or operator is missing
     *
     * @example
     * Input tokens: ["hub", "=", "[", "guid1", ",", "guid2", "]"]
     * Output: { type: "comparison", identifier: "hub", operator: "=", value: ["guid1", "guid2"] }
     *
     * @example
     * Input tokens: ["rating", ">=", "200"]
     * Output: { type: "comparison", identifier: "rating", operator: ">=", value: 200 }
     */
    parseComparison(): Statement {
        const field = this.next(); // e.g. "hub", "rating", "title"

        if (!field) {
            throw new Error(`Unexpected end of input at position ${this.pos}`);
        }

        const op = this.next(); // e.g. '=', '!=', '>=', '~', etc.

        if (!op) {
            throw new Error(`Unexpected end of input at position ${this.pos}`);
        }

        const value = this.parseValue();

        return {
            type: "comparison",
            identifier: field.image,
            operator: op.image as ComparisonOperator,
            value,
        };
    }

    /**
     * Parses values - either array literals or single literals.
     * Handles type conversion for strings, numbers, and special values.
     *
     * @returns The parsed value - either a single value or an array of values
     *
     * Array format: [value1, value2, ...]
     * - Strings are enclosed in quotes and get unquoted
     * - Special values like "3d" are kept as strings
     *
     * @example
     * Input tokens: ["[", '"guid1"', ",", '"guid2"', "]"]
     * Output: ["guid1", "guid2"]
     *
     * @example
     * Input tokens: ['"python ""cool"""']
     * Output: "python "cool""
     *
     * @example
     * Input tokens: ["3d"]
     * Output: "3d"
     */
    parseValue(): string | string[] {
        if (this.peek()?.image === "[") {
            this.next(); // consume '['
            const arr: string[] = [];

            while (this.peek()?.image !== "]") {
                let lit = this.next();

                if (!lit) {
                    throw new Error(
                        `Unexpected end of input at position ${this.pos}`,
                    );
                }

                let literal: string = lit.image;

                // Strip quotes if it's a quoted string
                if (literal.startsWith('"') && literal.endsWith('"')) {
                    literal = literal.slice(1, -1).replaceAll('""', '"');
                }

                arr.push(literal);

                // Consume comma if present
                if (this.peek()?.image === ",") {
                    this.next();
                }
            }

            this.eat("]"); // ensure closing bracket

            return arr;
        } else {
            // Single literal value
            let lit = this.next();

            if (!lit) {
                throw new Error(
                    `Unexpected end of input at position ${this.pos}`,
                );
            }

            let literal: string = lit.image;

            // Handle quoted strings
            if (literal.startsWith('"') && literal.endsWith('"')) {
                return literal.slice(1, -1).replaceAll('""', '"');
            }

            // Return as-is for special values like "3d", identifiers, etc.
            return literal;
        }
    }
}

export function createAst(tokens: IToken[]) {
    return new Parser(tokens).parse();
}
