export function pad2zeros(value: number) {
  return value.toString().padStart(2, "0");
}

export class NewCalendarDate extends Date {
  static readonly MS_PER_DAY = 1000 * 60 * 60 * 24;
  static readonly DAYS_PER_MONTH = 28;

  static readonly PEACE_DAY = 365;
  static readonly LEAP_DAY = 366;

  getIsLeapYear(year = this.getFullYear()) {
    return (year % 400 === 0) || (year % 4 === 0 && year % 100 !== 0);
  }

  getDayOfYear(year = this.getFullYear()) {
    const msFromStartOfYear = this.getTime() - new Date(year, 0, 0).getTime();

    return Math.floor(msFromStartOfYear / NewCalendarDate.MS_PER_DAY);
  }

  getDayPosition(dayOfYear: number): {
      month: number;
      day: number;
  } {
    if (dayOfYear === NewCalendarDate.LEAP_DAY) {
      return {
        month: 14,
        day: 2,
      };
    }

    if (dayOfYear === NewCalendarDate.PEACE_DAY) {
      return {
        month: 14,
        day: 1,
      };
    }

    return {
      month: Math.ceil(dayOfYear / NewCalendarDate.DAYS_PER_MONTH),
      day: (dayOfYear % NewCalendarDate.DAYS_PER_MONTH) || 28,
    };
  }

  getParsed() {
    const year = this.getFullYear();
    const dayOfYear = this.getDayOfYear(year);
    const { month, day } = this.getDayPosition(dayOfYear);

    return {
      year,
      month,
      day,
    };
  }

  toDateString() {
    const { year, month, day } = this.getParsed();

    return `${pad2zeros(day)}.${pad2zeros(month)}.${year}`;
  }

  toString() {
    const originalIsoString = super.toISOString();
    const timeFragment = originalIsoString.split("T")[1]!.slice(0, 8);

    const { year, month, day } = this.getParsed();

    return `${pad2zeros(day)}.${pad2zeros(month)}.${year} ${timeFragment}`;
  }

  toISODateString() {
    const { year, month, day } = this.getParsed();

    return `${year}-${pad2zeros(month)}-${pad2zeros(day)}`;
  }

  toISOString() {
    const originalIsoString = super.toISOString();
    const timeFragment = originalIsoString.split("T")[1]!;

    return `${this.toISODateString()}T${timeFragment}`;
  }
}
