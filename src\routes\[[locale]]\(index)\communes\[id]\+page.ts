import type { Localization } from "$lib";
import type { Commune } from "../+page";
import type { PageLoad } from "./$types";

type CommuneMemberType = "user" | "commune";

interface CommuneImage {
  id: string;
  url: string;
  source: string;
}

export interface CommuneMember {
  id: string;
  communeId: string;
  actorType: CommuneMemberType;
  actorId: string;
  isHead: boolean;
  joinedAt: string;
  leftAt: string | null;
  name?: Localization[];
  images?: CommuneImage[];
}

export const load: PageLoad = async ({ fetch, params }) => {
  const [
    commune,
    members,
  ] = await Promise.all([
    fetch(`/api/commune/${params.id}`).then<Commune>(r => r.json()),
    fetch(`/api/commune/${params.id}/member`).then<CommuneMember[]>(r => r.json()),
  ]);

  return {
    commune,
    members,
  };
};
