<script lang="ts">
  import type { Locale } from "$lib";

  import { NewCalendarDate } from "$lib";
  import { onMount } from "svelte";

  interface Props {
    locale: Locale;
  }

  const i18n = {
    en: {
      gregorianCalendar: "Gregorian Calendar",
      newCalendar: "New Calendar",
    },

    ru: {
      gregorianCalendar: "Григорианский календарь",
      newCalendar: "Новый календарь",
    },
  };

  const { locale }: Props = $props();

  const t = $derived(i18n[locale]);

  function formatTime(date: Date) {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  }

  let currentTime = $state(new Date(0));
  const formattedTime = $derived(formatTime(currentTime));

  onMount(() => {
    const timer = setInterval(() => {
      currentTime = new Date();
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  });
</script>

<div class="my-4">
  <div class="row">
    <div class="col-md-6">
      <div class="card mb-3">
        <div class="card-body">
          <h5 class="card-title text-center">{t.gregorianCalendar}</h5>
          <div class="display-4 text-center">
            {currentTime.toLocaleDateString([], {
              year: "numeric",
              month: "numeric",
              day: "numeric",
            })}
          </div>
          <div class="display-6 text-center font-monospace">{formattedTime}</div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card mb-3">
        <div class="card-body">
          <h5 class="card-title text-center">{t.newCalendar}</h5>
          <div class="display-4 text-center">
            {new NewCalendarDate(currentTime).toDateString()}
          </div>
          <div class="display-6 text-center font-monospace">{formattedTime}</div>
        </div>
      </div>
    </div>
  </div>
</div>
