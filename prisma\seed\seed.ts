import { Prisma, PrismaClient, UserRole } from "@prisma/client";
import { UUID_NIL } from "src/consts";
import { faker<PERSON> as Faker } from "@faker-js/faker";
const prisma = new PrismaClient();

const UUID_NIL_1 = UUID_NIL.slice(0, -1) + "1";
const UUID_NIL_2 = UUID_NIL.slice(0, -1) + "2";

const users = [
    {
        id: UUID_NIL,
        email: "<EMAIL>",
        role: "user",
        name: {
            en: "Test User",
            ru: "Тестовый Пользователь",
        },
        description: {
            en: "Test description.",
            ru: "Тестовое описание.",
        },
    },
    {
        id: UUID_NIL_1,
        email: "<EMAIL>",
        role: "admin",
        name: {
            en: "Admin",
            ru: "Администратор",
        },
        description: {
            en: "Admin description.",
            ru: "Описание администратора.",
        },
    },
    {
        id: UUID_NIL_2,
        email: "<EMAIL>",
        role: "user",
        name: {
            en: "User",
            ru: "Пользователь",
        },
        description: {
            en: "User description.",
            ru: "Описание пользователя.",
        },
    },
];

async function main() {
    await prisma.$transaction(async (trx) => {
        await Promise.all(
            users.map((user) =>
                trx.user.create({
                    data: {
                        id: user.id,
                        email: user.email,
                        role: user.role as UserRole,
                        name: {
                            create: [
                                {
                                    locale: "en",
                                    key: "name",
                                    value: user.name.en,
                                },
                                {
                                    locale: "ru",
                                    key: "name",
                                    value: user.name.ru,
                                },
                            ],
                        },
                        description: {
                            create: [
                                {
                                    locale: "en",
                                    key: "description",
                                    value: user.description.en,
                                },
                                {
                                    locale: "ru",
                                    key: "description",
                                    value: user.description.ru,
                                },
                            ],
                        },
                    },
                }),
            ),
        );

        // await trx.commune.create({
        //     data: {
        //         id: UUID_NIL,
        //         name: {
        //             create: [
        //                 {
        //                     locale: "en",
        //                     key: "name",
        //                     value: "Test Commune",
        //                 },
        //                 {
        //                     locale: "ru",
        //                     key: "name",
        //                     value: "Тестовая Коммуна",
        //                 },
        //             ],
        //         },
        //         description: {
        //             create: [
        //                 {
        //                     locale: "en",
        //                     key: "description",
        //                     value: "Test description.",
        //                 },
        //                 {
        //                     locale: "ru",
        //                     key: "description",
        //                     value: "Тестовое описание.",
        //                 },
        //             ],
        //         },
        //         members: {
        //             create: [
        //                 {
        //                     id: UUID_NIL,
        //                     actorType: "user",
        //                     actorId: UUID_NIL,
        //                     isHead: true,
        //                 },
        //             ],
        //         },
        //     },
        // });

        await seedManyCommunes(trx, 60);
    });
}

async function seedManyCommunes(trx: Prisma.TransactionClient, count = 20) {
    await Promise.all(
        new Array(count).fill(0).map(async (_, index) => {
            await trx.commune.create({
                data: {
                    name: {
                        create: [
                            {
                                locale: "en",
                                key: "name",
                                value: Faker.company.name(),
                            },
                        ],
                    },

                    members: {
                        create: {
                            actorType: "user",
                            actorId: UUID_NIL,
                            isHead: true,
                        },
                    },
                },
            });
        }),
    );
}

main()
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
