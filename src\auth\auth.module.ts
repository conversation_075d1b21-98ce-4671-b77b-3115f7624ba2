import { Modu<PERSON> } from "@nestjs/common";
import { SessionStrategy } from "./session.strategy";
import { UserModule } from "src/user/user.module";
import { EmailModule } from "src/email/email.module";
import { AuthService } from "./auth.service";
import { AuthController } from "./http/auth.controller";

@Module({
    imports: [EmailModule, UserModule],
    controllers: [AuthController],
    providers: [AuthService, SessionStrategy],
})
export class AuthModule {}
