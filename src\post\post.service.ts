import { ForbiddenException, Injectable } from "@nestjs/common";
import { Prisma, PostStatus, Locale } from "@prisma/client";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { toPrismaPagination } from "src/utils";
import { CurrentUser } from "src/auth/types";
import { ZodHelper } from "src/zod";
import { getError } from "src/common/errors";

@Injectable()
export class PostService extends BaseService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
    ) {
        super("post");
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        return true;
    }

    async check(ids: string[]) {
        return await this._check(ids, this.prisma.post, this.entityType);
    }

    async getOne(id: string) {
        const post = await this.prisma.post.findUnique({
            where: { id, deletedAt: null },
            include: {
                title: true,
                description: true,
                images: true,
            },
        });

        return post;
    }

    async getOneOrThrow(id: string) {
        const post = await this.getOne(id);

        if (!post) {
            throw this.createNotFoundException();
        }

        return post;
    }

    async getOneWithRole(id: string, user?: CurrentUser) {
        const post = await this.prisma.post.findUnique({
            where: { id, deletedAt: null },
            include: {
                title: true,
                description: true,
                images: true,
            },
        });

        // If post not found or user is admin, return as is
        if (!post || (user && user.role === "admin")) {
            return post;
        }

        // For non-admin users, only return published posts
        if (post.status !== "published") {
            return null;
        }

        return post;
    }

    async getMany(
        where: Prisma.PostWhereInput,
        pagination?: { page: number; size: number },
        user?: CurrentUser,
    ) {
        // For non-admin users, only show published posts
        const finalWhere = { ...where };
        if (!user || user.role !== "admin") {
            finalWhere.status = "published";

            // Only show posts that are published and have a publishedAt date in the past
            finalWhere.OR = [
                {
                    publishedAt: {
                        lte: new Date(),
                    },
                },
                {
                    publishedAt: null,
                },
            ];
        }

        return await this.prisma.post.findMany({
            ...toPrismaPagination(pagination),
            where: finalWhere,
            include: {
                title: true,
                description: true,
                images: true,
            },
        });
    }

    async create(
        data: {
            title: ZodHelper.Localization[];
            description: ZodHelper.Localization[];
            status: PostStatus;
            publishedAt: Date | null;
        },
        user: CurrentUser,
    ) {
        if (user.role !== "admin") {
            throw new ForbiddenException(...getError("must_be_admin"));
        }

        const post = await this.prisma.post.create({
            data: {
                title: {
                    create: data.title.map((item) => ({
                        ...item,
                        key: "title",
                    })),
                },
                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },
                status: data.status,
                publishedAt: data.publishedAt,
            },
            include: {
                title: true,
                description: true,
                images: true,
            },
        });

        console.log("created post", post.id);

        return post;
    }

    async update(id: string, data: Prisma.PostUpdateInput, user: CurrentUser) {
        await this.canChange(id, user);

        return await this.updateOne(id, data);
    }

    async updateOne(id: string, data: Prisma.PostUpdateInput) {
        return await this.prisma.post.update({
            where: { id, deletedAt: null },
            data,
            include: {
                title: true,
                description: true,
                images: true,
            },
        });
    }

    async uploadPostImages(postId: string, files: FileInfo[]) {
        await this.getOneOrThrow(postId);

        const images = await Promise.all(
            files.map(async (file, index) => {
                const imageUrl = await this.minioService.uploadPostImage(
                    file,
                    postId,
                    index,
                );

                const image = await this.prisma.image.create({
                    data: {
                        url: imageUrl,
                    },
                });

                return image;
            }),
        );

        await this.prisma.post.update({
            where: { id: postId },
            data: {
                images: {
                    connect: images.map((image) => ({ id: image.id })),
                },
            },
        });

        return images;
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
}
