<script lang="ts">
  import type { Locale } from "$lib";
  import type { DictionaryKey } from "./dictionaries";
  import type { DictionaryBreakdown } from "./examples";

  import { darkDictionaryColors, lightDictionaryColors } from "./dictionary-colors";
  import { examples } from "./examples";

  interface Props {
    locale: Locale;
  }

  const i18n = {
    en: {
      title: "Word Examples",
      description: "See how common English words transform in our phonetic system",
      hideDetails: "Hide Details",
      showDetails: "Show Details",
      soundBreakdown: "Sound Breakdown",
      phoneticSounds: "Phonetic Sounds",
      newEnglishLetter: "New English Letter(s)",
    },

    ru: {
      title: "Примеры слов",
      description:
        "Посмотрите, как обычные английские слова преобразуются в нашей фонетической системе",
      hideDetails: "Скрыть детали",
      showDetails: "Показать детали",
      soundBreakdown: "Разбор звуков",
      phoneticSounds: "Фонетические звуки",
      newEnglishLetter: "Буквы Нового английского",
    },
  };

  const { locale }: Props = $props();

  const t = $derived(i18n[locale]);

  let expandedExample = $state<number | null>(null);

  function switchExpandedExample(index: number) {
    if (expandedExample === index) {
      expandedExample = null;
    } else {
      expandedExample = index;
    }
  }
</script>

{#snippet mappings(item: DictionaryBreakdown)}
  {@const firstMapping = item[0].mapping}
  {@const allSameMapping = item.every((variant) => variant.mapping === firstMapping)}

  {#if allSameMapping}
    <!-- If all mappings are the same, display as "any" in black -->
    <span style:color="#000000">{firstMapping}</span>
  {:else}
    <!-- If mappings differ, display each with its dictionary color -->
    {#each item as variant, i}
      {#if i > 0}
        ,
      {/if}

      <span style:color={`#${darkDictionaryColors[variant.dictionaryKey]}`}>
        {variant.mapping}
      </span>
    {/each}
  {/if}
{/snippet}

<section class="mb-5">
  <h2>{t.title}</h2>
  <div class="card">
    <div class="card-body">
      <p class="lead">{t.description}:</p>
      <ul class="list-group list-group-flush">
        {#each examples as example, index}
          <li class="list-group-item">
            <div
              class="d-flex justify-content-between align-items-center cursor-pointer"
              onclick={() => switchExpandedExample(index)}
              style:cursor="pointer"
              role={expandedExample === index ? t.hideDetails : t.showDetails}
            >
              <div>
                <strong>{example.english}</strong>
                <span class=" ms-2">
                  {#each Object.entries(example.transcriptions) as [dict, transcription], i}
                    {#if i > 0}
                      ,
                    {/if}

                    <span style:color={`#${darkDictionaryColors[dict as DictionaryKey]}`}>
                      {transcription}
                    </span>
                  {/each}
                </span>
                <span class="ms-2">→</span>
                <span class="ms-2 fw-bold">
                  {#if typeof example.newEnglish === "string"}
                    <span class="text-dark">{example.newEnglish}</span>
                  {:else}
                    {#each Object.entries(example.newEnglish) as [dict, translation], i}
                      {#if i > 0}
                        ,
                      {/if}

                      <span style:color={`#${darkDictionaryColors[dict as DictionaryKey]}`}>
                        {translation}
                      </span>
                    {/each}
                  {/if}
                </span>
              </div>
              <button class="btn btn-sm btn-outline-primary">
                {expandedExample === index ? t.hideDetails : t.showDetails}
              </button>
            </div>

            {#if expandedExample === index}
              <div class="mt-3">
                <h6>{t.soundBreakdown}:</h6>
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>{t.phoneticSounds}</th>
                      <th>{t.newEnglishLetter}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each example.breakdown as item}
                      <!-- Handle both simple mapping and array of dictionary-specific mappings -->
                      {#if Array.isArray(item)}
                        <!-- This is an array of dictionary-specific mappings -->
                        <!-- Render all variants on the same line -->
                        <tr>
                          <td>
                            {#each item as variant}
                              <span class="me-2">
                                {#each variant.sounds as sound}
                                  <span
                                    class="badge rounded-pill me-1 font-monospace"
                                    style:background-color={`#${lightDictionaryColors[variant.dictionaryKey]}`}
                                    style:color="#000000"
                                    style:padding="6px 12px"
                                    style:margin-bottom="6px"
                                    style:font-size="1.2rem"
                                    style:font-weight="normal"
                                  >
                                    {sound}
                                  </span>
                                {/each}
                              </span>
                            {/each}
                          </td>
                          <td>
                            <strong
                              style:font-size="1.5rem"
                              style:display="inline-block"
                              style:padding="4px 0"
                            >
                              {@render mappings(item)}
                            </strong>
                          </td>
                        </tr>
                      {:else}
                        <!-- This is a simple mapping (consider it as "any") -->
                        <tr>
                          <td>
                            {#each item.sounds as sound}
                              <span
                                class="badge rounded-pill me-2 font-monospace"
                                style:background-color={`#${lightDictionaryColors["any"]}`}
                                style:color="#000000"
                                style:border="1px solid #CCCCCC"
                                style:padding="6px 12px"
                                style:margin-bottom="6px"
                                style:font-size="1.2rem"
                                style:font-weight="normal"
                              >
                                {sound}
                              </span>
                            {/each}
                          </td>
                          <td>
                            <strong
                              style:font-size="1.5rem"
                              style:display="inline-block"
                              style:padding="4px 0"
                            >
                              {item.mapping}
                            </strong>
                          </td>
                        </tr>
                      {/if}
                    {/each}
                  </tbody>
                </table>
              </div>
            {/if}
          </li>
        {/each}
      </ul>
    </div>
  </div>
</section>
