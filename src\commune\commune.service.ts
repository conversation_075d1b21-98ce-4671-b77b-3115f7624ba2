import { ForbiddenException, Injectable } from "@nestjs/common";
import { CommuneMemberType, Prisma, UserRole } from "@prisma/client";
import { toPrismaPagination } from "src/utils";
import { getError } from "src/common/errors";
import { BaseService } from "src/common/base-service";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { ZodHelper } from "src/zod";

@Injectable()
export class CommuneService extends BaseService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
    ) {
        super("commune");
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        return true;
    }

    override async canChange(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        const headMember = await this.getHeadMember(id);

        if (user.role !== UserRole.admin) {
            if (
                headMember.actorType === CommuneMemberType.user &&
                headMember.actorId !== user.id
            ) {
                throw new ForbiddenException(
                    ...getError("must_be_head_member"),
                );
            }
        }

        return true;
    }

    async check(ids: string[]) {
        return await this._check(ids, this.prisma.commune, this.entityType);
    }

    async getOne(id: string) {
        return await this.prisma.commune.findUnique({
            where: { id },
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }

    async getOneOrThrow(id: string) {
        const commune = await this.getOne(id);

        if (!commune) {
            throw this.createNotFoundException();
        }

        return commune;
    }

    async getMany(
        where: Prisma.CommuneWhereInput,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.commune.findMany({
            ...toPrismaPagination(pagination),
            where,
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }

    async createOne(data: Prisma.CommuneCreateInput) {
        return await this.prisma.commune.create({
            data,
        });
    }

    async createMany(data: Prisma.CommuneCreateManyInput[]) {
        return await this.prisma.commune.createMany({
            data,
        });
    }

    async create(
        data: {
            headUserId?: string;
            name: ZodHelper.Localization[];
            description: ZodHelper.Localization[];
        },
        user: CurrentUser,
    ) {
        if (!data.headUserId) {
            data.headUserId = user.id;
        }

        if (data.headUserId !== user.id) {
            if (user.role !== "admin") {
                throw new ForbiddenException(
                    ...getError("head_user_id_mismatch"),
                );
            }
        }

        const commune = await this.prisma.commune.create({
            data: {
                members: {
                    create: {
                        actorType: CommuneMemberType.user,
                        actorId: data.headUserId!,
                        isHead: true,
                    },
                },
                name: {
                    create: data.name.map((item) => ({
                        ...item,
                        key: "name",
                    })),
                },
                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },
            },
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });

        return commune;
    }

    async uploadCommuneImages(communeId: string, files: FileInfo[]) {
        await this.getOneOrThrow(communeId);

        const images = await Promise.all(
            files.map(async (file, index) => {
                const imageUrl = await this.minioService.uploadCommuneImage(
                    file,
                    communeId,
                    index,
                );

                const image = await this.prisma.image.create({
                    data: {
                        url: imageUrl,
                    },
                });

                return image;
            }),
        );

        await this.prisma.commune.update({
            where: { id: communeId },
            data: {
                images: {
                    connect: images.map((image) => ({ id: image.id })),
                },
            },
        });

        return images;
    }

    async updateOne(id: string, data: Prisma.CommuneUpdateInput) {
        return await this.prisma.commune.update({
            where: { id },
            data,
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }

    async getHeadMember(id: string) {
        return await this.prisma.communeMember.findFirstOrThrow({
            where: {
                communeId: id,
                isHead: true,
                deletedAt: null,
            },
        });
    }

    async update(
        id: string,
        data: Prisma.CommuneUpdateInput,
        user: CurrentUser,
    ) {
        const commune = await this.getOne(id);

        if (!commune) {
            throw this.createNotFoundException();
        }

        if (user.role !== "admin") {
            const headMember = await this.getHeadMember(id);

            if (
                headMember.actorType === CommuneMemberType.user &&
                headMember.actorId !== user.id
            ) {
                throw new ForbiddenException(
                    ...getError("must_be_head_member"),
                );
            }
        }

        return await this.updateOne(id, data);
    }

    async updateMany(
        where: Prisma.CommuneWhereInput,
        data: Prisma.CommuneUpdateInput,
    ) {
        return await this.prisma.commune.updateMany({ where, data });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteOneCascade(id: string, user: CurrentUser) {
        const commune = await this.getOne(id);

        if (!commune) {
            throw this.createNotFoundException();
        }

        if (user.role !== "admin") {
            const headMember = await this.getHeadMember(id);

            if (
                headMember.actorType === CommuneMemberType.user &&
                headMember.actorId !== user.id
            ) {
                throw new ForbiddenException(
                    ...getError("must_be_head_member"),
                );
            }
        }

        await this.prisma.$transaction(async (trx) => {
            const now = new Date();

            await trx.commune.update({
                where: { id },
                data: { deletedAt: now },
            });

            await trx.communeMember.updateMany({
                where: { communeId: id },
                data: { deletedAt: now },
            });
        });
    }

    async softDeleteMany(where: Prisma.CommuneWhereInput) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }

    async deleteOne(id: string) {
        return await this.prisma.commune.delete({ where: { id } });
    }

    async deleteMany(where: Prisma.CommuneWhereInput) {
        return await this.prisma.commune.deleteMany({ where });
    }
}
