import type { Localization } from "$lib";
import { redirect } from "@sveltejs/kit";
import type { PageLoad } from "./$types";

export type Me = {
  id: string;
  email: string;
  role: "user" | "admin";
  name: Localization[];
  description: Localization[];
  images: {
    id: string;
    url: string;
    source: string;
  }[];
  joinedAt: string;
};

export const load: PageLoad = async ({ fetch, url }) => {
  const response = await fetch("/api/auth/me");

  if (response.status === 401) {
    // redirect to /auth?redirectFrom=${url.pathname}?search
    throw redirect(302, `/auth?redirectFrom=${encodeURIComponent(url.pathname + url.search)}`);
  }

  const me: Me = await response.json();

  return { me };
};
