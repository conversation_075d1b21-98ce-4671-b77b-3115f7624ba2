import { z } from "zod";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";

export const votesRequired = z.number().int().positive();

export type Voting = ZodHelper.Infer<typeof Voting>;
export const Voting = z.object({
    id: ZodHelper.Uuid,

    votesRequired,
    endsAt: <PERSON>od<PERSON>elper.ToDateTime,

    title: ZodHelper.Localizations,
    description: ZodHelper.Localizations,

    options: z.array(
        z.object({
            id: ZodHelper.Uuid,

            title: ZodHelper.Localizations,
        }),
    ),

    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const Votings = z.array(Voting);

export type CreateVoting = ZodHelper.Infer<typeof CreateVoting>;
export const CreateVoting = z.object({
    votesRequired,
    endsAt: Zod<PERSON>elper.ToDateTime,

    title: <PERSON>odHelper.Localizations,
    description: ZodHelper.Localizations,

    options: z.array(
        z.object({
            title: <PERSON>od<PERSON>el<PERSON>.Localizations,
        }),
    ),
});
