import type { PageLoad } from "./$types";
import type { Localization } from "$lib";

import { redirect } from "@sveltejs/kit";

export interface User {
  id: string;
  email: string;
  role: "user" | "admin";
  name: Localization[];
  description: Localization[];
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
}

export const load: PageLoad = async ({ fetch, url }) => {
  const response = await fetch("/api/user");

  if (response.status === 401) {
    // redirect to /auth?redirectFrom=${url.pathname}?search
    throw redirect(302, `/auth?redirectFrom=${encodeURIComponent(url.pathname + url.search)}`);
  }

  const data: User[] = response.ok ? await response.json() : [];

  return {
    users: data,
    isHasMoreUsers: data.length === 20, // If we got a full page, there might be more
  };
};
