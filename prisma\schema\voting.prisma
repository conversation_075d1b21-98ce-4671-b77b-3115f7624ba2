// voting
model Voting {
    @@map("votings")

    id String @id @db.Uuid @default(uuid(7))

    images  Image[]        @relation("voting_images")
    options VotingOption[] @relation("voting_options")
    votes   Vote[]         @relation("voting_votes")
    tags    Tag[]          @relation("voting_tags")

    title       Localization[] @relation("voting_title")
    description Localization[] @relation("voting_description")

    votesRequired Int @map("votes_required")

    endsAt DateTime @map("ends_at") @db.Timestamptz(3)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

model VotingOption {
    @@map("voting_options")

    id String @id @db.Uuid @default(uuid(7))

    votingId String @map("voting_id") @db.Uuid
    voting   Voting @relation("voting_options", fields: [votingId], references: [id])

    votes Vote[] @relation("voting_votes")

    title       Localization[] @relation("voting_option_title")
    description Localization[] @relation("voting_option_description")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// vote-actor-type
enum VoteActorType {
    @@map("vote_actor_type")

    commune
    user
}

// vote
model Vote {
    @@map("votes")

    id String @id @db.Uuid @default(uuid(7))

    actorType VoteActorType
    actorId   String @map("actor_id") @db.Uuid

    votingId String? @map("voting_id") @db.Uuid
    voting   Voting? @relation("voting_votes", fields: [votingId], references: [id])

    optionId String?       @map("option_id") @db.Uuid
    option   VotingOption? @relation("voting_votes", fields: [optionId], references: [id])

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
