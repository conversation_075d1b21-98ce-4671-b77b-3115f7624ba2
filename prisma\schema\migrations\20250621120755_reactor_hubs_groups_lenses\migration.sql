/*
  Warnings:

  - You are about to drop the column `isActive` on the `user_titles` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "reactor_posts" ADD COLUMN     "group_id" UUID,
ADD COLUMN     "hub_id" UUID;

-- AlterTable
ALTER TABLE "user_titles" DROP COLUMN "isActive",
ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "reactor_hubs" (
    "id" UUID NOT NULL,
    "head_user_id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_hubs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_groups" (
    "id" UUID NOT NULL,
    "hub_id" UUID NOT NULL,
    "head_user_id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_groups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_lenses" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "sql" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_lenses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_reactor_hub_name" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_reactor_hub_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_hub_description" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_reactor_hub_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_group_name" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_reactor_group_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_group_description" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_reactor_group_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "reactor_groups_hub_id_idx" ON "reactor_groups"("hub_id");

-- CreateIndex
CREATE INDEX "_reactor_hub_name_B_index" ON "_reactor_hub_name"("B");

-- CreateIndex
CREATE INDEX "_reactor_hub_description_B_index" ON "_reactor_hub_description"("B");

-- CreateIndex
CREATE INDEX "_reactor_group_name_B_index" ON "_reactor_group_name"("B");

-- CreateIndex
CREATE INDEX "_reactor_group_description_B_index" ON "_reactor_group_description"("B");

-- CreateIndex
CREATE INDEX "reactor_posts_hub_id_idx" ON "reactor_posts"("hub_id");

-- CreateIndex
CREATE INDEX "reactor_posts_group_id_idx" ON "reactor_posts"("group_id");

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_hub_id_fkey" FOREIGN KEY ("hub_id") REFERENCES "reactor_hubs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "reactor_groups"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_hubs" ADD CONSTRAINT "reactor_hubs_head_user_id_fkey" FOREIGN KEY ("head_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_groups" ADD CONSTRAINT "reactor_groups_hub_id_fkey" FOREIGN KEY ("hub_id") REFERENCES "reactor_hubs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_groups" ADD CONSTRAINT "reactor_groups_head_user_id_fkey" FOREIGN KEY ("head_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_lenses" ADD CONSTRAINT "reactor_lenses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_name" ADD CONSTRAINT "_reactor_hub_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_name" ADD CONSTRAINT "_reactor_hub_name_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_hubs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_description" ADD CONSTRAINT "_reactor_hub_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_description" ADD CONSTRAINT "_reactor_hub_description_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_hubs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_group_name" ADD CONSTRAINT "_reactor_group_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_group_name" ADD CONSTRAINT "_reactor_group_name_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_group_description" ADD CONSTRAINT "_reactor_group_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_group_description" ADD CONSTRAINT "_reactor_group_description_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;
