<script lang="ts">
  import type { Localization } from "$lib";
  import type { PostEntity } from "./[id]/types";

  import { onMount } from "svelte";
  import { fetchWithAuth } from "$lib";
  import { Modal } from "$lib/components";
  import PostCard from "./post-card.svelte";
  import RightMenu from "./right-menu.svelte";
  import LocalizedInput from "../(index)/components/localized-input.svelte";
  import LocalizedTextarea from "../(index)/components/localized-textarea.svelte";
  import { goto } from "$app/navigation";

  // Create Post i18n
  const i18n = {
    en: {
      _page: {
        title: "Feed — Reactor",
      },
      createPost: "Create Post",
      createPostTitle: "Create New Post",
      cancel: "Cancel",
      create: "Create",
      title: "Title",
      titlePlaceholder: "Enter post title...",
      body: "Body",
      bodyPlaceholder: "Write your post content...",
      titleRequired: "Title is required",
      bodyRequired: "Body is required",
      createSuccess: "Post created successfully!",
      createError: "Failed to create post",
    },
    ru: {
      _page: {
        title: "Лента — Реактор",
      },
      createPost: "Создать пост",
      createPostTitle: "Создать новый пост",
      cancel: "Отмена",
      create: "Создать",
      title: "Заголовок",
      titlePlaceholder: "Введите заголовок поста...",
      body: "Содержание",
      bodyPlaceholder: "Напишите содержание поста...",
      titleRequired: "Заголовок обязателен",
      bodyRequired: "Содержание обязательно",
      createSuccess: "Пост успешно создан!",
      createError: "Не удалось создать пост",
    },
  };

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref } = $derived(data);

  const t = $derived(i18n[locale]);

  let page = $state(1);
  let posts = $state<PostEntity[] | null>(null);

  let isRightMenuExpanded = $state(false);

  let showCreatePostModal = $state(false);

  // Form state
  let postTitle = $state<Localization[]>([]);
  let postBody = $state<Localization[]>([]);
  let isSubmitting = $state(false);
  let formError = $state<string | null>(null);
  let formSuccess = $state<string | null>(null);

  function openCreatePostModal() {
    // Reset form state
    postTitle = [];
    postBody = [];
    formError = null;
    formSuccess = null;
    isSubmitting = false;
    showCreatePostModal = true;
  }

  function closeCreatePostModal() {
    showCreatePostModal = false;
  }

  async function handleCreatePost() {
    // Clear previous messages
    formError = null;
    formSuccess = null;

    // Validate form
    const hasTitle = postTitle.some((item) => item.value.trim().length > 0);
    const hasBody = postBody.some((item) => item.value.trim().length > 0);

    if (!hasTitle) {
      formError = t.titleRequired;
      return;
    }

    if (!hasBody) {
      formError = t.bodyRequired;
      return;
    }

    isSubmitting = true;

    try {
      const response = await fetchWithAuth("/api/reactor/post", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: postTitle.filter((item) => item.value.trim().length > 0),
          body: postBody.filter((item) => item.value.trim().length > 0),
          tags: [], // Empty tags array for now
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.createError);
      }

      formSuccess = t.createSuccess;

      const data = (await response.json()) as {
        id: string;
      };

      setTimeout(() => {
        goto(toLocaleHref(`/reactor/${data.id}`));
      }, 1500);
    } catch (error) {
      console.error("Error creating post:", error);
      formError = error instanceof Error ? error.message : t.createError;
    } finally {
      isSubmitting = false;
    }
  }

  async function fetchPosts() {
    const response = await fetchWithAuth(`/api/reactor/post?page=${page}&size=20`);

    if (response.ok) {
      const data = (await response.json()) as {
        items: PostEntity[];
        total: number;
      };

      const fetchedPosts = data.items.map((post) => ({
        ...post,

        createdAt: new Date(post.createdAt),
        updatedAt: new Date(post.updatedAt),
      }));

      (posts ??= []).push(...fetchedPosts);
    }
  }

  onMount(fetchPosts);
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="row g-4 mt-3">
  <div class="col-1"></div>

  <!-- Left Menu (2-3 columns) -->
  <div class="col-2">
    <!-- <LeftMenu {locale} /> -->
  </div>

  <!-- Feed (4-9 columns) -->
  <div class="col-6">
    <div class="feed">
      {#if posts}
        {#each posts as post (post.id)}
          <PostCard {post} {locale} {routeLocale} {toLocaleHref} />
        {/each}
      {:else}
        <div class="text-center">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Right Menu (10-11 columns) -->
  <div class="col-2">
    <!-- Create Post Button -->
    <div
      class="mb-3 create-post-btn-container {isRightMenuExpanded ? 'with-right-menu-expanded' : ''}"
    >
      <button
        class="btn btn-outline-secondary w-100 create-post-btn"
        onclick={openCreatePostModal}
        aria-label={t.createPost}
      >
        <i class="bi bi-plus-circle me-2"></i>
        {t.createPost}
      </button>
    </div>

    <RightMenu {locale} {toLocaleHref} bind:isExpanded={isRightMenuExpanded} />
  </div>
</div>

<!-- Create Post Modal -->
<Modal
  show={showCreatePostModal}
  title={t.createPostTitle}
  onClose={closeCreatePostModal}
  onSubmit={handleCreatePost}
  submitText={t.create}
  cancelText={t.cancel}
  submitDisabled={isSubmitting ||
    !postTitle.some((item) => item.value.trim().length > 0) ||
    !postBody.some((item) => item.value.trim().length > 0)}
  {isSubmitting}
>
  <form>
    <!-- Title Input -->
    <LocalizedInput
      {locale}
      id="post-title"
      label={t.title}
      placeholder={t.titlePlaceholder}
      required
      bind:value={postTitle}
    />

    <!-- Body Textarea -->
    <LocalizedTextarea
      {locale}
      id="post-body"
      label={t.body}
      placeholder={t.bodyPlaceholder}
      rows={6}
      required
      bind:value={postBody}
    />

    <!-- Error Message -->
    {#if formError}
      <div class="alert alert-danger mt-3" role="alert">
        {formError}
      </div>
    {/if}

    <!-- Success Message -->
    {#if formSuccess}
      <div class="alert alert-success mt-3" role="alert">
        {formSuccess}
      </div>
    {/if}
  </form>
</Modal>

<style lang="scss">
  .feed {
    max-width: 100%;
  }

  .create-post-btn-container {
    opacity: 0.5;

    &:hover,
    &.with-right-menu-expanded {
      opacity: 1;
    }
  }

  @media (max-width: 767.98px) {
    .feed {
      margin-top: 1rem;
    }
  }

  .create-post-btn {
    transition: all 0.2s ease;
  }

  .create-post-btn:hover {
    border-color: var(--bs-success);
    color: var(--bs-success);
    background-color: transparent;
  }
</style>
