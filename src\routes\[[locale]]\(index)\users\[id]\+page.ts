import type { Localization } from "$lib";
import type { PageLoad } from "./$types";
import { redirect } from "@sveltejs/kit";

export interface User {
  id: string;
  email: string;
  role: "user" | "admin" | "moderator";
  name: Localization[];
  description: Localization[];
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export const load: PageLoad = async ({ fetch, params, url }) => {
  const response = await fetch(`/api/user/${params.id}`);

  if (response.status === 401) {
    // redirect to /auth?redirectFrom=${url.pathname}?search
    throw redirect(302, `/auth?redirectFrom=${encodeURIComponent(url.pathname + url.search)}`);
  }

  if (!response.ok) {
    // Handle other errors (404, 500, etc.)
    throw new Error(`Failed to fetch user: ${response.statusText}`);
  }

  const user: User = await response.json();

  return {
    user,
  };
};
