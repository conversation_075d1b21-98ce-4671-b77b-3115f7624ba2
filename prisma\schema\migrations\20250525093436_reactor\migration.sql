CREATE EXTENSION IF NOT EXISTS ltree;

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "reactor_entity_type" AS ENUM ('post', 'comment');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "reactor_rating_type" AS ENUM ('like', 'dislike');

-- CreateTable
CREATE TABLE "reactor_posts" (
    "id" UUID NOT NULL,
    "author_id" UUID NOT NULL,
    "is_anonymous" BOOLEAN NOT NULL DEFAULT false,
    "anonimity_reason" TEXT,
    "delete_reason" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_posts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_post_internal_numbers" (
    "id" UUID NOT NULL,
    "post_id" UUID NOT NULL,
    "internal_number" INTEGER NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "reactor_post_internal_numbers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_comments" (
    "id" UUID NOT NULL,
    "author_id" UUID NOT NULL,
    "post_id" UUID NOT NULL,
    "path" LTREE NOT NULL,
    "internal_number" INTEGER NOT NULL,
    "is_anonymous" BOOLEAN NOT NULL DEFAULT false,
    "anonimity_reason" TEXT,
    "delete_reason" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_comments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_ratings" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "entity_type" "reactor_entity_type" NOT NULL,
    "entity_id" UUID NOT NULL,
    "type" "reactor_rating_type" NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "reactor_ratings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_usefulnesses" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "entity_type" "reactor_entity_type" NOT NULL,
    "entity_id" UUID NOT NULL,
    "value" INTEGER NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "reactor_usefulnesses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_reactor_post_title" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_reactor_post_title_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_post_body" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_reactor_post_body_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_comment_body" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_reactor_comment_body_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_post_tags" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_reactor_post_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "reactor_posts_author_id_idx" ON "reactor_posts"("author_id");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_post_internal_numbers_post_id_key" ON "reactor_post_internal_numbers"("post_id");

-- CreateIndex
CREATE INDEX "reactor_comments_post_id_path_idx" ON "reactor_comments"("post_id", "path");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_comments_post_id_internal_number_key" ON "reactor_comments"("post_id", "internal_number");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_comments_post_id_path_key" ON "reactor_comments"("post_id", "path");

-- CreateIndex
CREATE INDEX "reactor_ratings_user_id_idx" ON "reactor_ratings"("user_id");

-- CreateIndex
CREATE INDEX "reactor_ratings_entity_type_entity_id_idx" ON "reactor_ratings"("entity_type", "entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_ratings_user_id_entity_type_entity_id_key" ON "reactor_ratings"("user_id", "entity_type", "entity_id");

-- CreateIndex
CREATE INDEX "reactor_usefulnesses_user_id_idx" ON "reactor_usefulnesses"("user_id");

-- CreateIndex
CREATE INDEX "reactor_usefulnesses_entity_type_entity_id_idx" ON "reactor_usefulnesses"("entity_type", "entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_usefulnesses_user_id_entity_type_entity_id_key" ON "reactor_usefulnesses"("user_id", "entity_type", "entity_id");

-- CreateIndex
CREATE INDEX "_reactor_post_title_B_index" ON "_reactor_post_title"("B");

-- CreateIndex
CREATE INDEX "_reactor_post_body_B_index" ON "_reactor_post_body"("B");

-- CreateIndex
CREATE INDEX "_reactor_comment_body_B_index" ON "_reactor_comment_body"("B");

-- CreateIndex
CREATE INDEX "_reactor_post_tags_B_index" ON "_reactor_post_tags"("B");

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_comments" ADD CONSTRAINT "reactor_comments_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_ratings" ADD CONSTRAINT "reactor_ratings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_usefulnesses" ADD CONSTRAINT "reactor_usefulnesses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_title" ADD CONSTRAINT "_reactor_post_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_title" ADD CONSTRAINT "_reactor_post_title_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_body" ADD CONSTRAINT "_reactor_post_body_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_body" ADD CONSTRAINT "_reactor_post_body_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_comment_body" ADD CONSTRAINT "_reactor_comment_body_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_comment_body" ADD CONSTRAINT "_reactor_comment_body_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_comments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_tags" ADD CONSTRAINT "_reactor_post_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_tags" ADD CONSTRAINT "_reactor_post_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;
