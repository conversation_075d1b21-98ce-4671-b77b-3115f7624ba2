import { Prisma } from "@prisma/client";
import { Injectable } from "@nestjs/common";
import { CurrentUser } from "src/auth/types";
import { toPrismaPagination } from "src/utils";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class VotingOptionService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super("voting-option");
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        return true;
    }

    async check(ids: string[]) {
        return await this._check(
            ids,
            this.prisma.votingOption,
            "voting_option",
        );
    }

    async getOne(id: string) {
        return await this.prisma.votingOption.findUnique({
            where: { id, deletedAt: null },
        });
    }

    async getOneOrThrow(id: string) {
        const votingOption = await this.getOne(id);

        if (!votingOption) {
            throw this.createNotFoundException();
        }

        return votingOption;
    }

    async getMany(
        where: Prisma.VotingOptionWhereInput,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.votingOption.findMany({
            ...toPrismaPagination(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }

    async createOne(data: Prisma.VotingOptionCreateInput) {
        return await this.prisma.votingOption.create({ data });
    }

    async createMany(data: Prisma.VotingOptionCreateManyInput[]) {
        return await this.prisma.votingOption.createMany({ data });
    }

    async updateOne(id: string, data: Prisma.VotingOptionUpdateInput) {
        return await this.prisma.votingOption.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }

    async updateMany(
        where: Prisma.VotingOptionWhereInput,
        data: Prisma.VotingOptionUpdateInput,
    ) {
        return await this.prisma.votingOption.updateMany({ where, data });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteMany(where: Prisma.VotingOptionWhereInput) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }

    async deleteOne(id: string) {
        return await this.prisma.votingOption.delete({ where: { id } });
    }

    async deleteMany(where: Prisma.VotingOptionWhereInput) {
        return await this.prisma.votingOption.deleteMany({ where });
    }
}
