import { goto } from "$app/navigation";
import { removeCurrentUser } from "./current-user";

/**
 * A utility function for making HTTP requests with session-based authentication handling
 */
export async function fetchWithAuth(
  input: RequestInfo | URL,
  init?: RequestInit,
): Promise<Response> {
  // Ensure credentials are included for session cookies
  const requestInit: RequestInit = {
    ...init,
    credentials: "include",
  };

  // Call the native fetch function with the provided parameters
  const response = await fetch(input, requestInit);

  // If the response status is 401 (Unauthorized), redirect to login
  if (response.status === 401) {
    // Clear local user data since session is invalid
    removeCurrentUser();
    return redirectToLoginPage();
  }

  return response;
}

function redirectToLoginPage(): Promise<never> {
  // Get current path
  const currentPath = window.location.pathname + window.location.search;
  const encodedPath = encodeURIComponent(currentPath);

  // Use SvelteKit's goto for navigation
  goto(`/auth?redirectFrom=${encodedPath}`);

  // Return a promise that never resolves since we're redirecting
  return new Promise(() => {});
}
