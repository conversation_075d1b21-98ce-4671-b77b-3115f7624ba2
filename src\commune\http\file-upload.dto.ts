import { z } from "zod";

// Define allowed file types
export const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const MAX_FILES_COUNT = 10;

// Validation schema for file uploads
export const FileUploadSchema = z.object({
    mimetype: z.enum(ALLOWED_FILE_TYPES as [string, ...string[]]),
    size: z.number().max(MAX_FILE_SIZE),
    originalname: z.string(),
    buffer: z.instanceof(Buffer),
});

export type FileUpload = z.infer<typeof FileUploadSchema>;
