import { Injectable } from "@nestjs/common";
import { PrismaService } from "src/prisma/prisma.service";
import { Prisma, Localization } from "@prisma/client";
import { <PERSON>od<PERSON>el<PERSON> } from "src/zod";
import { toPrismaPagination } from "src/utils";

type GetLocalization = {
    entityType: string;
    entityId: string;
    key: string;
    locales: ZodHelper.Locale[];
};

type SaveLocalization = {
    entityType: string;
    entityId: string;
    key: string;
    locale: ZodHelper.Locale;
    value: string;
};

const defaultLocale: ZodHelper.Locale = "en";

@Injectable()
export class LocalizationService {
    constructor(private readonly prisma: PrismaService) {}

    protected getFirstNonNullValue(
        localizations: Normalize<Pick<Localization, "locale" | "value">>[],
        locales: ZodHelper.Locale[],
    ): string | null {
        const localeLocalizationMap = new Map(
            localizations.map((localization) => [
                localization.locale,
                localization.value,
            ]),
        );

        for (const locale of locales) {
            if (localeLocalizationMap.has(locale)) {
                return localeLocalizationMap.get(locale)!;
            }
        }

        if (localeLocalizationMap.has(defaultLocale)) {
            return localeLocalizationMap.get(defaultLocale)!;
        }

        return localizations[0]?.value ?? null;
    }

    async getOne(id: string) {
        return await this.prisma.localization.findUnique({
            where: { id, deletedAt: null },
        });
    }

    async getMany(
        where: Prisma.LocalizationWhereInput,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.localization.findMany({
            ...toPrismaPagination(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }

    async createOne(data: Prisma.LocalizationCreateInput) {
        return await this.prisma.localization.create({ data });
    }

    async createMany(data: Prisma.LocalizationCreateManyInput[]) {
        return await this.prisma.localization.createMany({ data });
    }

    async updateOne(id: string, data: Prisma.LocalizationUpdateInput) {
        return await this.prisma.localization.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }

    async updateMany(
        where: Prisma.LocalizationWhereInput,
        data: Prisma.LocalizationUpdateInput,
    ) {
        return await this.prisma.localization.updateMany({ where, data });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteMany(where: Prisma.LocalizationWhereInput) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }

    async deleteOne(id: string) {
        return await this.prisma.localization.delete({ where: { id } });
    }

    async deleteMany(where: Prisma.LocalizationWhereInput) {
        return await this.prisma.localization.deleteMany({ where });
    }
}
