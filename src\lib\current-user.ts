import { browser } from "$app/environment";
import { LocalStorageUser } from "./dto";

export function getCurrentUser(): LocalStorageUser | null {
  if (!browser) {
    return null;
  }

  const maybeUser = localStorage.getItem("user");

  if (maybeUser) {
    try {
      const jsonUser = JSON.parse(maybeUser);

      const user = LocalStorageUser.safeParse(jsonUser);

      if (user.success) {
        return user.data;
      }

      console.error("Invalid user in localStorage", user.error);
    }
    catch (error) {
      console.error("Error parsing user from localStorage", error);
    }
  }

  removeCurrentUser();

  return null;
}

export function setCurrentUser(user: LocalStorageUser) {
  if (!browser) {
    return;
  }

  localStorage.setItem("user", JSON.stringify(user));
}

export function removeCurrentUser() {
  if (!browser) {
    return;
  }

  localStorage.removeItem("user");
}
