import { BadRequestException, Injectable, PipeTransform } from "@nestjs/common";
import { ZodSchema } from "zod";

@Injectable()
export class ZodPipe implements PipeTransform {
    constructor(private readonly schema: ZodSchema) {}

    transform(value: any) {
        const result = this.schema.safeParse(value);

        if (!result.success) {
            throw new BadRequestException(result.error.errors);
        }

        return result.data;
    }
}
