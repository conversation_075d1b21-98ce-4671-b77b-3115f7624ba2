<script lang="ts">
  import type { Image, Locale } from "$lib";

  import { onMount } from "svelte";

  interface Props {
    locale: Locale;
    images: Image[];
  }

  const i18n = {
    en: {
      noImage: "No image",
      userImageAlt: "User image",
      previous: "Previous",
      next: "Next",
    },
    ru: {
      noImage: "Нет изображения",
      userImageAlt: "Изображение пользователя",
      previous: "Предыдущий",
      next: "Следующий",
    },
  };

  const { locale, images }: Props = $props();

  const t = $derived(i18n[locale]);

  let activeIndex = $state(0);
  let carouselElement = $state<HTMLElement | null>(null);

  function nextSlide() {
    activeIndex = (activeIndex + 1) % images.length;
  }

  function prevSlide() {
    activeIndex = (activeIndex - 1 + images.length) % images.length;
  }

  function goToSlide(index: number) {
    activeIndex = index;
  }

  // Set up carousel functionality
  onMount(() => {
    if (carouselElement && images.length > 1) {
      // Optional: Add event listeners for keyboard navigation
      const handleKeydown = (e: KeyboardEvent) => {
        if (e.key === "ArrowLeft") prevSlide();
        if (e.key === "ArrowRight") nextSlide();
      };

      document.addEventListener("keydown", handleKeydown);

      return () => {
        document.removeEventListener("keydown", handleKeydown);
      };
    }
  });
</script>

{#if !images || images.length === 0}
  <div
    class="bg-light text-center d-flex align-items-center justify-content-center"
    style="height: 140px;"
  >
    <span class="text-muted">{t.noImage}</span>
  </div>
{:else}
  <div style="height: 140px; overflow: hidden;">
    <div id="userImageCarousel" class="carousel slide" bind:this={carouselElement}>
      {#if images.length > 1}
        <!-- Carousel indicators -->
        <div class="carousel-indicators">
          {#each images as _, index}
            <button
              type="button"
              data-bs-target="#userImageCarousel"
              data-bs-slide-to={index}
              class={activeIndex === index ? "active" : ""}
              aria-current={activeIndex === index ? "true" : "false"}
              aria-label={`Slide ${index + 1}`}
              onclick={() => goToSlide(index)}
            ></button>
          {/each}
        </div>
      {/if}

      <!-- Carousel items -->
      <div class="carousel-inner">
        {#each images as image, index}
          <div class={`carousel-item ${activeIndex === index ? "active" : ""}`}>
            <div
              style="height: 140px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;"
            >
              <img
                src={`/api/images/${image.url}`}
                alt={`${t.userImageAlt} ${index + 1}`}
                style="width: 100%; height: 100%; object-fit: cover;"
              />
            </div>
          </div>
        {/each}
      </div>

      {#if images.length > 1}
        <!-- Carousel controls -->
        <button
          class="carousel-control-prev"
          type="button"
          onclick={prevSlide}
          aria-label={t.previous}
        >
          <span class="carousel-control-prev-icon" aria-hidden="true"></span>
          <span class="visually-hidden">{t.previous}</span>
        </button>
        <button class="carousel-control-next" type="button" onclick={nextSlide} aria-label={t.next}>
          <span class="carousel-control-next-icon" aria-hidden="true"></span>
          <span class="visually-hidden">{t.next}</span>
        </button>
      {/if}
    </div>
  </div>
{/if}
