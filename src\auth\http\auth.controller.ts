import {
    Controller,
    Post,
    Body,
    Get,
    UseGuards,
    Ip,
    Headers,
    UnauthorizedException,
    Req,
    HttpStatus,
    HttpCode,
    Res,
} from "@nestjs/common";
import { z } from "zod";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";
import { UserService } from "src/user/user.service";
import { CurrentUser } from "../types";
import { AuthService } from "../auth.service";
import * as Dto from "./dto";
import { HttpSessionAuthGuard } from "./session-auth.guard";
import { HttpCurrentUser } from "./current-user.decorator";
import { Request, Response } from "express";

@Controller("auth")
export class AuthController {
    constructor(
        private readonly authService: AuthService,
        private readonly userService: UserService,
    ) {}

    @Get("test")
    async test() {
        return true;
    }

    @Get("me")
    @UseGuards(HttpSessionAuthGuard)
    async me(@HttpCurrentUser() currentUser: CurrentUser) {
        const user = await this.userService.getOne(currentUser.id);

        if (!user) {
            throw new UnauthorizedException();
        }

        return ZodHelper.parseInput(Dto.Me, {
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.name,
            description: user.description,
            images: user.images,
            joinedAt: user.createdAt,
        });
    }

    @Post("otp")
    @HttpCode(HttpStatus.CREATED)
    async otp(
        @Body(new ZodPipe(Dto.Otp)) body: Dto.Otp,
        @Ip() ipAddress: string,
        @Headers("user-agent") userAgent: string,
    ) {
        const isSent = await this.authService.otp({
            ...body,
            ipAddress,
            userAgent,
        });

        return ZodHelper.parseInput(z.object({ isSent: z.boolean() }), {
            isSent,
        });
    }

    @Post("register")
    @HttpCode(HttpStatus.CREATED)
    async register(
        @Req() req: Request,
        @Body(new ZodPipe(Dto.Register)) body: Dto.Register,
        @Ip() ipAddress: string,
        @Headers("user-agent") userAgent: string,
    ) {
        const { user } = await this.authService.register({
            ...body,
            referrerId: body.referrerId ?? null,
            ipAddress,
            userAgent,
        });

        // Store user in session
        req.session.user = user;

        return user;
    }

    @Post("login")
    @HttpCode(HttpStatus.OK)
    async login(
        @Req() req: Request,
        @Body(new ZodPipe(Dto.Login)) body: Dto.Login,
        @Ip() ipAddress: string,
        @Headers("user-agent") userAgent: string,
    ) {
        const { user } = await this.authService.login({
            ...body,
            ipAddress,
            userAgent,
        });

        // Store user in session
        req.session.user = user;

        return user;
    }

    @Get("sign-out")
    @HttpCode(HttpStatus.OK)
    async signOut(@Req() req: Request, @Res() res: Response) {
        // Destroy the session
        req.session.destroy((err) => {
            if (err) {
                console.error("Error destroying session:", err);
            }

            res.clearCookie("session");
            res.sendStatus(HttpStatus.OK);
        });
    }
}
