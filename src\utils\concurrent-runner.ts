/**
 * A utility class for running an array of promise-returning functions with controlled concurrency.
 */
export class ConcurrentRunner {
    /**
     * Executes an array of functions that return promises with a maximum concurrency limit.
     *
     * @param tasks - Array of functions that return promises
     * @param maxConcurrent - Maximum number of promises that can run simultaneously (default: 5)
     * @returns Promise that resolves with an array of all results in the same order as the input tasks
     */
    static async run<T>(
        tasks: Array<() => Promise<T>>,
        maxConcurrent: number = 5,
    ): Promise<T[]> {
        const results: T[] = new Array(tasks.length);
        let currentIndex = 0;

        // Function to process a single task
        async function processTask(taskIndex: number): Promise<void> {
            results[taskIndex] = await tasks[taskIndex]!();

            // If there are more tasks, process the next one
            if (currentIndex < tasks.length) {
                const nextIndex = currentIndex++;
                await processTask(nextIndex);
            }
        }

        // Start initial batch of tasks up to maxConcurrent
        const initialBatch: Promise<void>[] = [];
        const tasksToStart = Math.min(maxConcurrent, tasks.length);

        for (let i = 0; i < tasksToStart; i++) {
            initialBatch.push(processTask(currentIndex++));
        }

        // Wait for all tasks to complete
        await Promise.all(initialBatch);

        return results;
    }
}
