# Redirect all HTTP traffic to HTTPS
server {
  listen 80;
  server_name mail.dev.commune.my;

  # Optional: Let’s Encrypt challenge responses if needed
  location /.well-known/acme-challenge/ {
    root /var/www/html;
  }

  # Priority route for /robots.txt: disallow indexing
  location = /robots.txt {
    add_header Content-Type text/plain;
    return 200 "User-agent: *\nDisallow: /";
  }

  # Redirect all other requests to HTTPS
  location / {
    return 301 https://$host$request_uri;
  }
}

# HTTPS server block
server {
  listen 443 ssl http2;
  server_name mail.dev.commune.my;

  # SSL certificate files (adjust paths as needed)
  ssl_certificate /etc/letsencrypt/live/mail.dev.commune.my/fullchain.pem;
  ssl_certificate_key /etc/letsencrypt/live/mail.dev.commune.my/privkey.pem;

  # Recommended SSL settings
  ssl_session_timeout 1d;
  ssl_session_cache shared:SSL:50m;
  ssl_protocols TLSv1.2 TLSv1.3;
  ssl_ciphers HIGH:!aNULL:!MD5;

  # Priority route for /robots.txt (for extra precaution)
  location = /robots.txt {
    add_header Content-Type text/plain;
    return 200 "User-agent: *\nDisallow: /";
  }

  # Proxy all other requests to the Poste.io container (HTTP on port 3010)
  location / {
    proxy_pass http://127.0.0.1:3010; # TODO: 3011
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
  }
}
