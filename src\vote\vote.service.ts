import { ForbiddenException, Injectable } from "@nestjs/common";
import { Prisma, UserRole, VoteActorType } from "@prisma/client";
import { CurrentUser } from "src/auth/types";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
import { toPrismaPagination } from "src/utils";

type CreateDto = {
    userId: string;
    votingId: string;
    optionId: string;
};

@Injectable()
export class VoteService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super("vote");
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        if (user.role !== UserRole.admin) {
            throw new ForbiddenException();
        }

        return true;
    }

    async check(ids: string[]) {
        return await this._check(ids, this.prisma.vote, "vote");
    }

    async getOne(id: string) {
        return await this.prisma.vote.findUnique({
            where: { id, deletedAt: null },
        });
    }

    async getOneOrThrow(id: string) {
        const vote = await this.getOne(id);

        if (!vote) {
            throw this.createNotFoundException();
        }

        return vote;
    }

    async getMany(
        where: Prisma.VoteWhereInput,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.vote.findMany({
            ...toPrismaPagination(pagination),

            where: {
                ...where,
                deletedAt: null,
            },
        });
    }

    async createOne(data: Prisma.VoteCreateInput) {
        return await this.prisma.vote.create({ data });
    }

    async createMany(data: Prisma.VoteCreateManyInput[]) {
        return await this.prisma.vote.createMany({ data });
    }

    async create(data: CreateDto) {
        await this.prisma.$transaction(async (trx) => {
            await trx.vote.deleteMany({
                where: {
                    actorType: VoteActorType.user,
                    actorId: data.userId,
                    votingId: data.votingId,
                },
            });

            await trx.vote.create({
                data: {
                    actorType: VoteActorType.user,
                    actorId: data.userId,
                    votingId: data.votingId,
                    optionId: data.optionId,
                },
            });
        });
    }

    async updateOne(id: string, data: Prisma.VoteUpdateInput) {
        return await this.prisma.vote.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }

    async updateMany(
        where: Prisma.VoteWhereInput,
        data: Prisma.VoteUpdateInput,
    ) {
        return await this.prisma.vote.updateMany({ where, data });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteMany(where: Prisma.VoteWhereInput) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }

    async deleteOne(id: string) {
        return await this.prisma.vote.delete({ where: { id } });
    }

    async deleteMany(where: Prisma.VoteWhereInput) {
        return await this.prisma.vote.deleteMany({ where });
    }
}
