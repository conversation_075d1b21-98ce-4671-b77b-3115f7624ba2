import {
    Controller,
    Get,
    Post,
    Body,
    Param,
    Delete,
    Query,
} from "@nestjs/common";
import { VotingService } from "../voting.service";
import * as Dto from "./dto";
import { Zod<PERSON>ipe, ZodHelper } from "src/zod";

@Controller("voting")
export class VotingController {
    constructor(private readonly votingService: VotingService) {}

    @Get()
    async getVotings(
        @Query("page", new ZodPipe(ZodHelper.pagination.page)) page: number,
        @Query("size", new ZodPipe(ZodHelper.pagination.size)) size: number,
    ): Promise<Dto.Voting[]> {
        const votings = await this.votingService.getMany(
            {
                deletedAt: null,
            },
            { page, size },
        );

        return ZodHelper.parseInput(Dto.Votings, votings);
    }

    @Post()
    async createVoting(
        @Body(new ZodPipe(Dto.CreateVoting)) body: Dto.CreateVoting,
    ): Promise<Dto.Voting> {
        const voting = await this.votingService.create(body);

        return ZodHelper.parseInput(Dto.Voting, voting);
    }

    @Get(":id")
    async getVoting(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<Dto.Voting> {
        const voting = await this.votingService.getOneOrThrow(id);

        return ZodHelper.parseInput(Dto.Voting, voting);
    }

    @Delete(":id")
    async deleteVoting(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<void> {
        await this.votingService.deleteOne(id);
    }
}
