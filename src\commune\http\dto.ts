import * as prisma from "@prisma/client";
import { z, ZodHelper } from "src/zod";

export type CommuneMemberType = ZodHelper.Infer<typeof CommuneMemberType>;
export const CommuneMemberType = z.nativeEnum(prisma.CommuneMemberType);

export type CommuneMember = ZodHelper.Infer<typeof CommuneMember>;
export const CommuneMember = z.object({
    id: ZodHelper.Uuid,

    communeId: ZodHelper.Uuid,

    actorType: CommuneMemberType,
    actorId: ZodHelper.Uuid,

    name: ZodHelper.Localizations,
    images: z.array(ZodHelper.Image),

    joinedAt: ZodHelper.ToDateTime,
    leftAt: ZodHelper.ToDateTime.nullable(),
});

export const CommuneMembers = z.array(CommuneMember);

export type Commune = ZodHelper.Infer<typeof Commune>;
export const Commune = z.object({
    id: ZodHelper.Uuid,

    name: <PERSON>od<PERSON>el<PERSON>.Localizations,
    description: ZodHelper.Localizations,

    headMember: z.object({
        actorType: CommuneMemberType,
        actorId: ZodHelper.Uuid,

        name: ZodHelper.Localizations,
    }),

    memberCount: ZodHelper.positiveIntSchema,

    images: z.array(ZodHelper.Image).optional(),

    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const Communes = z.array(Commune);

export type CreateCommuneInput = ZodHelper.Infer<typeof CreateCommuneInput>;
export const CreateCommuneInput = ZodHelper.JsonToObject({
    headUserId: ZodHelper.Uuid.optional(),

    name: ZodHelper.Localizations,
    description: ZodHelper.Localizations,

    // Images will be handled separately via file upload
});

export type UpdateCommuneInput = ZodHelper.Infer<typeof UpdateCommuneInput>;
export const UpdateCommuneInput = z.object({
    name: ZodHelper.Localizations,
    description: ZodHelper.Localizations,
});

export type CreateCommuneMemberInput = ZodHelper.Infer<
    typeof CreateCommuneMemberInput
>;
export const CreateCommuneMemberInput = z.object({
    actorType: CommuneMemberType,
    actorId: ZodHelper.Uuid,
});

export type UpdateCommuneMemberInput = ZodHelper.Infer<
    typeof UpdateCommuneMemberInput
>;
export const UpdateCommuneMemberInput = z.object({});
